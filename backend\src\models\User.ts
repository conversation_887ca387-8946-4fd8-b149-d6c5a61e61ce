import {
  Table,
  Column,
  Model,
  <PERSON>Type,
  <PERSON><PERSON>ey,
  <PERSON><PERSON>ult,
  Unique,
  AllowNull,
  CreatedAt,
  UpdatedAt,
  HasMany,
  BeforeCreate,
  BeforeUpdate,
} from 'sequelize-typescript';
import bcrypt from 'bcryptjs';
import { Project } from './Project';
import { Notification } from './Notification';

export interface UserAttributes {
  id: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: 'admin' | 'user';
  isActive: boolean;
  isEmailVerified: boolean;
  emailVerificationToken?: string;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  lastLoginAt?: Date;
  loginCount: number;
  settings: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserCreationAttributes extends Omit<UserAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

@Table({
  tableName: 'users',
  timestamps: true,
  indexes: [
    { fields: ['email'] },
    { fields: ['role'] },
    { fields: ['isActive'] },
    { fields: ['emailVerificationToken'] },
    { fields: ['passwordResetToken'] },
  ],
})
export class User extends Model<UserAttributes, UserCreationAttributes> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @Unique
  @AllowNull(false)
  @Column(DataType.STRING)
  email!: string;

  @AllowNull(false)
  @Column(DataType.STRING)
  password!: string;

  @AllowNull(false)
  @Column(DataType.STRING)
  firstName!: string;

  @AllowNull(false)
  @Column(DataType.STRING)
  lastName!: string;

  @Column(DataType.STRING)
  avatar?: string;

  @Default('user')
  @Column(DataType.ENUM('admin', 'user'))
  role!: 'admin' | 'user';

  @Default(true)
  @Column(DataType.BOOLEAN)
  isActive!: boolean;

  @Default(false)
  @Column(DataType.BOOLEAN)
  isEmailVerified!: boolean;

  @Column(DataType.STRING)
  emailVerificationToken?: string;

  @Column(DataType.STRING)
  passwordResetToken?: string;

  @Column(DataType.DATE)
  passwordResetExpires?: Date;

  @Column(DataType.DATE)
  lastLoginAt?: Date;

  @Default(0)
  @Column(DataType.INTEGER)
  loginCount!: number;

  @Default({})
  @Column(DataType.JSONB)
  settings!: Record<string, any>;

  @CreatedAt
  createdAt!: Date;

  @UpdatedAt
  updatedAt!: Date;

  // Associations
  @HasMany(() => Project)
  projects!: Project[];

  @HasMany(() => Notification)
  notifications!: Notification[];

  // Hooks
  @BeforeCreate
  @BeforeUpdate
  static async hashPassword(instance: User) {
    if (instance.changed('password')) {
      const salt = await bcrypt.genSalt(12);
      instance.password = await bcrypt.hash(instance.password, salt);
    }
  }

  // Instance methods
  async comparePassword(candidatePassword: string): Promise<boolean> {
    return bcrypt.compare(candidatePassword, this.password);
  }

  async updateLastLogin(): Promise<void> {
    this.lastLoginAt = new Date();
    this.loginCount += 1;
    await this.save();
  }

  getFullName(): string {
    return `${this.firstName} ${this.lastName}`.trim();
  }

  toJSON(): Partial<UserAttributes> {
    const values = { ...this.get() };
    delete values.password;
    delete values.emailVerificationToken;
    delete values.passwordResetToken;
    delete values.passwordResetExpires;
    return values;
  }

  // Static methods
  static async findByEmail(email: string): Promise<User | null> {
    return this.findOne({
      where: { email: email.toLowerCase() },
    });
  }

  static async findByEmailVerificationToken(token: string): Promise<User | null> {
    return this.findOne({
      where: { emailVerificationToken: token },
    });
  }

  static async findByPasswordResetToken(token: string): Promise<User | null> {
    return this.findOne({
      where: {
        passwordResetToken: token,
        passwordResetExpires: {
          [this.sequelize!.Op.gt]: new Date(),
        },
      },
    });
  }

  static async createUser(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role?: 'admin' | 'user';
  }): Promise<User> {
    return this.create({
      ...userData,
      email: userData.email.toLowerCase(),
      role: userData.role || 'user',
      isActive: true,
      isEmailVerified: false,
      loginCount: 0,
      settings: {},
    });
  }

  static async updatePassword(userId: string, newPassword: string): Promise<boolean> {
    const [affectedCount] = await this.update(
      { password: newPassword },
      { where: { id: userId } }
    );
    return affectedCount > 0;
  }

  static async verifyEmail(token: string): Promise<boolean> {
    const [affectedCount] = await this.update(
      {
        isEmailVerified: true,
        emailVerificationToken: null,
      },
      {
        where: { emailVerificationToken: token },
      }
    );
    return affectedCount > 0;
  }

  static async setPasswordResetToken(email: string, token: string, expiresAt: Date): Promise<boolean> {
    const [affectedCount] = await this.update(
      {
        passwordResetToken: token,
        passwordResetExpires: expiresAt,
      },
      {
        where: { email: email.toLowerCase() },
      }
    );
    return affectedCount > 0;
  }

  static async resetPassword(token: string, newPassword: string): Promise<boolean> {
    const [affectedCount] = await this.update(
      {
        password: newPassword,
        passwordResetToken: null,
        passwordResetExpires: null,
      },
      {
        where: {
          passwordResetToken: token,
          passwordResetExpires: {
            [this.sequelize!.Op.gt]: new Date(),
          },
        },
      }
    );
    return affectedCount > 0;
  }

  static async deactivateUser(userId: string): Promise<boolean> {
    const [affectedCount] = await this.update(
      { isActive: false },
      { where: { id: userId } }
    );
    return affectedCount > 0;
  }

  static async activateUser(userId: string): Promise<boolean> {
    const [affectedCount] = await this.update(
      { isActive: true },
      { where: { id: userId } }
    );
    return affectedCount > 0;
  }

  static async updateSettings(userId: string, settings: Record<string, any>): Promise<boolean> {
    const user = await this.findByPk(userId);
    if (!user) return false;

    user.settings = { ...user.settings, ...settings };
    await user.save();
    return true;
  }

  static async getUserStats(userId: string): Promise<{
    projectCount: number;
    keywordCount: number;
    auditCount: number;
    lastActivity: Date | null;
  }> {
    const user = await this.findByPk(userId, {
      include: [
        {
          model: Project,
          include: ['keywords', 'audits'],
        },
      ],
    });

    if (!user) {
      return {
        projectCount: 0,
        keywordCount: 0,
        auditCount: 0,
        lastActivity: null,
      };
    }

    const projectCount = user.projects.length;
    const keywordCount = user.projects.reduce((sum, project) => sum + (project.keywords?.length || 0), 0);
    const auditCount = user.projects.reduce((sum, project) => sum + (project.audits?.length || 0), 0);

    return {
      projectCount,
      keywordCount,
      auditCount,
      lastActivity: user.lastLoginAt,
    };
  }
}
