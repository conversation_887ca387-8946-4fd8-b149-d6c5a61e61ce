import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON>ey,
  <PERSON><PERSON><PERSON>,
  AllowNull,
  CreatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Keyword } from './Keyword';

export interface KeywordHistoryAttributes {
  id: string;
  keywordId: string;
  position: number | null;
  url: string | null;
  checkedAt: Date;
  createdAt: Date;
}

export interface KeywordHistoryCreationAttributes extends Omit<KeywordHistoryAttributes, 'id' | 'createdAt'> {}

@Table({
  tableName: 'keyword_history',
  timestamps: false,
  indexes: [
    { fields: ['keywordId'] },
    { fields: ['checkedAt'] },
    { fields: ['position'] },
    { fields: ['keywordId', 'checkedAt'] },
  ],
})
export class KeywordHistory extends Model<KeywordHistoryAttributes, KeywordHistoryCreationAttributes> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Keyword)
  @AllowNull(false)
  @Column(DataType.UUID)
  keywordId!: string;

  @Column(DataType.INTEGER)
  position!: number | null;

  @Column(DataType.STRING)
  url!: string | null;

  @AllowNull(false)
  @Column(DataType.DATE)
  checkedAt!: Date;

  @CreatedAt
  createdAt!: Date;

  // Associations
  @BelongsTo(() => Keyword)
  keyword!: Keyword;

  // Static methods
  static async getHistoryForKeyword(
    keywordId: string,
    options?: {
      days?: number;
      limit?: number;
    }
  ): Promise<KeywordHistory[]> {
    const where: any = { keywordId };

    if (options?.days) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - options.days);
      where.checkedAt = {
        [this.sequelize!.Op.gte]: cutoffDate,
      };
    }

    return this.findAll({
      where,
      order: [['checkedAt', 'DESC']],
      limit: options?.limit,
    });
  }

  static async getHistoryForProject(
    projectId: string,
    options?: {
      days?: number;
      limit?: number;
    }
  ): Promise<KeywordHistory[]> {
    const where: any = {};

    if (options?.days) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - options.days);
      where.checkedAt = {
        [this.sequelize!.Op.gte]: cutoffDate,
      };
    }

    return this.findAll({
      where,
      include: [
        {
          model: Keyword,
          where: { projectId },
          required: true,
        },
      ],
      order: [['checkedAt', 'DESC']],
      limit: options?.limit,
    });
  }

  static async cleanupOldHistory(daysToKeep: number = 90): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const deletedCount = await this.destroy({
      where: {
        checkedAt: {
          [this.sequelize!.Op.lt]: cutoffDate,
        },
      },
    });

    return deletedCount;
  }
}
