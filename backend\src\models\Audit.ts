import {
  Table,
  Column,
  Model,
  <PERSON>Type,
  <PERSON><PERSON>ey,
  <PERSON><PERSON><PERSON>,
  AllowNull,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Project } from './Project';
import { AuditResult } from './AuditResult';

export interface AuditAttributes {
  id: string;
  projectId: string;
  name: string;
  description?: string;
  urls: string[];
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  settings: {
    device: 'desktop' | 'mobile';
    throttling: boolean;
    categories: string[];
    budget?: {
      performance?: number;
      accessibility?: number;
      bestPractices?: number;
      seo?: number;
      pwa?: number;
    };
  };
  results: {
    totalUrls: number;
    completedUrls: number;
    failedUrls: number;
    averageScores: {
      performance: number;
      accessibility: number;
      bestPractices: number;
      seo: number;
      pwa: number;
    };
  };
  startedAt: Date | null;
  completedAt: Date | null;
  duration: number | null;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuditCreationAttributes extends Omit<AuditAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

@Table({
  tableName: 'audits',
  timestamps: true,
  indexes: [
    { fields: ['projectId'] },
    { fields: ['status'] },
    { fields: ['startedAt'] },
    { fields: ['completedAt'] },
  ],
})
export class Audit extends Model<AuditAttributes, AuditCreationAttributes> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Project)
  @AllowNull(false)
  @Column(DataType.UUID)
  projectId!: string;

  @AllowNull(false)
  @Column(DataType.STRING)
  name!: string;

  @Column(DataType.TEXT)
  description?: string;

  @AllowNull(false)
  @Column(DataType.ARRAY(DataType.STRING))
  urls!: string[];

  @Default('pending')
  @Column(DataType.ENUM('pending', 'running', 'completed', 'failed', 'cancelled'))
  status!: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

  @Default(0)
  @Column(DataType.FLOAT)
  progress!: number;

  @Default({
    device: 'desktop',
    throttling: true,
    categories: ['performance', 'accessibility', 'best-practices', 'seo', 'pwa'],
  })
  @Column(DataType.JSONB)
  settings!: AuditAttributes['settings'];

  @Default({
    totalUrls: 0,
    completedUrls: 0,
    failedUrls: 0,
    averageScores: {
      performance: 0,
      accessibility: 0,
      bestPractices: 0,
      seo: 0,
      pwa: 0,
    },
  })
  @Column(DataType.JSONB)
  results!: AuditAttributes['results'];

  @Column(DataType.DATE)
  startedAt!: Date | null;

  @Column(DataType.DATE)
  completedAt!: Date | null;

  @Column(DataType.INTEGER)
  duration!: number | null;

  @Column(DataType.TEXT)
  error?: string;

  @CreatedAt
  createdAt!: Date;

  @UpdatedAt
  updatedAt!: Date;

  // Associations
  @BelongsTo(() => Project)
  project!: Project;

  @HasMany(() => AuditResult)
  auditResults!: AuditResult[];

  // Instance methods
  start(): void {
    this.status = 'running';
    this.startedAt = new Date();
    this.progress = 0;
    this.results.totalUrls = this.urls.length;
  }

  updateProgress(completedUrls: number, failedUrls: number = 0): void {
    this.results.completedUrls = completedUrls;
    this.results.failedUrls = failedUrls;
    this.progress = (completedUrls + failedUrls) / this.results.totalUrls;
  }

  complete(): void {
    this.status = 'completed';
    this.completedAt = new Date();
    this.progress = 1;
    
    if (this.startedAt) {
      this.duration = this.completedAt.getTime() - this.startedAt.getTime();
    }
  }

  fail(error: string): void {
    this.status = 'failed';
    this.completedAt = new Date();
    this.error = error;
    
    if (this.startedAt) {
      this.duration = this.completedAt.getTime() - this.startedAt.getTime();
    }
  }

  cancel(): void {
    this.status = 'cancelled';
    this.completedAt = new Date();
    
    if (this.startedAt) {
      this.duration = this.completedAt.getTime() - this.startedAt.getTime();
    }
  }

  async calculateAverageScores(): Promise<void> {
    const results = await AuditResult.findAll({
      where: { auditId: this.id },
      attributes: ['scores'],
    });

    if (results.length === 0) return;

    const totals = {
      performance: 0,
      accessibility: 0,
      bestPractices: 0,
      seo: 0,
      pwa: 0,
    };

    results.forEach(result => {
      totals.performance += result.scores.performance || 0;
      totals.accessibility += result.scores.accessibility || 0;
      totals.bestPractices += result.scores.bestPractices || 0;
      totals.seo += result.scores.seo || 0;
      totals.pwa += result.scores.pwa || 0;
    });

    const count = results.length;
    this.results.averageScores = {
      performance: Math.round((totals.performance / count) * 100) / 100,
      accessibility: Math.round((totals.accessibility / count) * 100) / 100,
      bestPractices: Math.round((totals.bestPractices / count) * 100) / 100,
      seo: Math.round((totals.seo / count) * 100) / 100,
      pwa: Math.round((totals.pwa / count) * 100) / 100,
    };
  }

  // Static methods
  static async findByProjectId(
    projectId: string,
    options?: {
      status?: AuditAttributes['status'][];
      limit?: number;
      offset?: number;
    }
  ): Promise<{ audits: Audit[]; total: number }> {
    const where: any = { projectId };

    if (options?.status && options.status.length > 0) {
      where.status = {
        [this.sequelize!.Op.in]: options.status,
      };
    }

    const { count, rows } = await this.findAndCountAll({
      where,
      include: [
        {
          model: AuditResult,
          required: false,
        },
      ],
      order: [['createdAt', 'DESC']],
      limit: options?.limit,
      offset: options?.offset,
    });

    return { audits: rows, total: count };
  }

  static async createAudit(auditData: {
    projectId: string;
    name: string;
    description?: string;
    urls: string[];
    settings?: Partial<AuditAttributes['settings']>;
  }): Promise<Audit> {
    const defaultSettings: AuditAttributes['settings'] = {
      device: 'desktop',
      throttling: true,
      categories: ['performance', 'accessibility', 'best-practices', 'seo', 'pwa'],
    };

    return this.create({
      ...auditData,
      status: 'pending',
      progress: 0,
      settings: { ...defaultSettings, ...auditData.settings },
      results: {
        totalUrls: auditData.urls.length,
        completedUrls: 0,
        failedUrls: 0,
        averageScores: {
          performance: 0,
          accessibility: 0,
          bestPractices: 0,
          seo: 0,
          pwa: 0,
        },
      },
      startedAt: null,
      completedAt: null,
      duration: null,
    });
  }

  static async getRunningAudits(): Promise<Audit[]> {
    return this.findAll({
      where: { status: 'running' },
      include: [{ model: Project }],
    });
  }

  static async getPendingAudits(): Promise<Audit[]> {
    return this.findAll({
      where: { status: 'pending' },
      include: [{ model: Project }],
      order: [['createdAt', 'ASC']],
    });
  }

  static async cancelAudit(auditId: string): Promise<boolean> {
    const audit = await this.findByPk(auditId);
    if (!audit || !['pending', 'running'].includes(audit.status)) {
      return false;
    }

    audit.cancel();
    await audit.save();
    return true;
  }

  static async retryAudit(auditId: string): Promise<boolean> {
    const audit = await this.findByPk(auditId);
    if (!audit || audit.status !== 'failed') {
      return false;
    }

    audit.status = 'pending';
    audit.progress = 0;
    audit.error = undefined;
    audit.startedAt = null;
    audit.completedAt = null;
    audit.duration = null;
    audit.results.completedUrls = 0;
    audit.results.failedUrls = 0;

    await audit.save();
    return true;
  }
}
