import Bull from 'bull';
import { config } from '../utils/config';
import { logger } from '../utils/logger';
import { serpService } from '../services/serpService';
import { lighthouseService } from '../services/lighthouseService';

// Create job queues
export const keywordQueue = new Bull('keyword tracking', {
  redis: config.redis.url,
  defaultJobOptions: {
    removeOnComplete: 10,
    removeOnFail: 5,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
});

export const auditQueue = new Bull('lighthouse audits', {
  redis: config.redis.url,
  defaultJobOptions: {
    removeOnComplete: 5,
    removeOnFail: 3,
    attempts: 2,
    backoff: {
      type: 'exponential',
      delay: 5000,
    },
  },
});

export const maintenanceQueue = new Bull('maintenance tasks', {
  redis: {
    host: config.redis.host,
    port: config.redis.port,
    password: config.redis.password,
  },
  defaultJobOptions: {
    removeOnComplete: 3,
    removeOnFail: 2,
    attempts: 1,
  },
});

// Keyword tracking job processors
keywordQueue.process('update-project-keywords', async (job) => {
  const { projectId } = job.data;
  logger.info(`Processing keyword update job for project ${projectId}`);
  
  try {
    const result = await serpService.updateKeywordPositions(projectId);
    logger.info(`Keyword update completed for project ${projectId}:`, result);
    return result;
  } catch (error) {
    logger.error(`Keyword update failed for project ${projectId}:`, error);
    throw error;
  }
});

keywordQueue.process('update-all-keywords', async (job) => {
  logger.info('Processing global keyword update job');
  
  try {
    const result = await serpService.updateAllProjects();
    logger.info('Global keyword update completed:', result);
    return result;
  } catch (error) {
    logger.error('Global keyword update failed:', error);
    throw error;
  }
});

// Lighthouse audit job processors
auditQueue.process('run-audit', async (job) => {
  const { auditId } = job.data;
  logger.info(`Processing Lighthouse audit job for audit ${auditId}`);
  
  try {
    await lighthouseService.runBatchAudit(auditId);
    logger.info(`Lighthouse audit completed for audit ${auditId}`);
    return { auditId, status: 'completed' };
  } catch (error) {
    logger.error(`Lighthouse audit failed for audit ${auditId}:`, error);
    throw error;
  }
});

// Maintenance job processors
maintenanceQueue.process('cleanup-old-data', async (job) => {
  const { daysToKeep = 90 } = job.data;
  logger.info(`Processing cleanup job, keeping ${daysToKeep} days of data`);
  
  try {
    // Clean up old keyword history
    // const keywordHistoryDeleted = await KeywordHistory.cleanupOldHistory(daysToKeep);
    
    // Clean up old audit reports
    const reportsDeleted = await lighthouseService.cleanupOldReports(daysToKeep);
    
    const result = {
      // keywordHistoryDeleted,
      reportsDeleted,
    };
    
    logger.info('Cleanup job completed:', result);
    return result;
  } catch (error) {
    logger.error('Cleanup job failed:', error);
    throw error;
  }
});

// Job event handlers
keywordQueue.on('completed', (job, result) => {
  logger.info(`Keyword job ${job.id} completed:`, result);
});

keywordQueue.on('failed', (job, err) => {
  logger.error(`Keyword job ${job.id} failed:`, err);
});

auditQueue.on('completed', (job, result) => {
  logger.info(`Audit job ${job.id} completed:`, result);
});

auditQueue.on('failed', (job, err) => {
  logger.error(`Audit job ${job.id} failed:`, err);
});

maintenanceQueue.on('completed', (job, result) => {
  logger.info(`Maintenance job ${job.id} completed:`, result);
});

maintenanceQueue.on('failed', (job, err) => {
  logger.error(`Maintenance job ${job.id} failed:`, err);
});

// Schedule recurring jobs
export const scheduleRecurringJobs = () => {
  // Update all keywords every hour
  keywordQueue.add(
    'update-all-keywords',
    {},
    {
      repeat: { cron: '0 * * * *' }, // Every hour
      jobId: 'hourly-keyword-update',
    }
  );

  // Cleanup old data daily at 2 AM
  maintenanceQueue.add(
    'cleanup-old-data',
    { daysToKeep: 90 },
    {
      repeat: { cron: '0 2 * * *' }, // Daily at 2 AM
      jobId: 'daily-cleanup',
    }
  );

  logger.info('Recurring jobs scheduled');
};

// Helper functions for adding jobs
export const addKeywordUpdateJob = (projectId: string, delay?: number) => {
  return keywordQueue.add(
    'update-project-keywords',
    { projectId },
    delay ? { delay } : undefined
  );
};

export const addAuditJob = (auditId: string, delay?: number) => {
  return auditQueue.add(
    'run-audit',
    { auditId },
    delay ? { delay } : undefined
  );
};

export const addCleanupJob = (daysToKeep: number = 90) => {
  return maintenanceQueue.add('cleanup-old-data', { daysToKeep });
};

// Queue monitoring
export const getQueueStats = async () => {
  const [keywordStats, auditStats, maintenanceStats] = await Promise.all([
    {
      waiting: await keywordQueue.getWaiting(),
      active: await keywordQueue.getActive(),
      completed: await keywordQueue.getCompleted(),
      failed: await keywordQueue.getFailed(),
    },
    {
      waiting: await auditQueue.getWaiting(),
      active: await auditQueue.getActive(),
      completed: await auditQueue.getCompleted(),
      failed: await auditQueue.getFailed(),
    },
    {
      waiting: await maintenanceQueue.getWaiting(),
      active: await maintenanceQueue.getActive(),
      completed: await maintenanceQueue.getCompleted(),
      failed: await maintenanceQueue.getFailed(),
    },
  ]);

  return {
    keyword: {
      waiting: keywordStats.waiting.length,
      active: keywordStats.active.length,
      completed: keywordStats.completed.length,
      failed: keywordStats.failed.length,
    },
    audit: {
      waiting: auditStats.waiting.length,
      active: auditStats.active.length,
      completed: auditStats.completed.length,
      failed: auditStats.failed.length,
    },
    maintenance: {
      waiting: maintenanceStats.waiting.length,
      active: maintenanceStats.active.length,
      completed: maintenanceStats.completed.length,
      failed: maintenanceStats.failed.length,
    },
  };
};

// Graceful shutdown
export const closeQueues = async () => {
  await Promise.all([
    keywordQueue.close(),
    auditQueue.close(),
    maintenanceQueue.close(),
  ]);
  logger.info('All job queues closed');
};
