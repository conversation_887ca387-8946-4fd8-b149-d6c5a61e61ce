import { Sequelize } from 'sequelize-typescript';
import path from 'path';
import { config } from './config';
import { logger, loggers } from './logger';

// Import models
import { User } from '../models/User';
import { Project } from '../models/Project';
import { Keyword } from '../models/Keyword';
import { KeywordHistory } from '../models/KeywordHistory';
import { Audit } from '../models/Audit';
import { AuditResult } from '../models/AuditResult';
import { Setting } from '../models/Setting';
import { Notification } from '../models/Notification';

class Database {
  public sequelize: Sequelize;

  constructor() {
    this.sequelize = this.createConnection();
    this.setupEventListeners();
  }

  private createConnection(): Sequelize {
    const sequelize = new Sequelize(config.database.url, {
      dialect: config.database.dialect,
      logging: config.database.logging ? (sql: string) => logger.debug(sql) : false,
      models: [
        User,
        Project,
        Keyword,
        KeywordHistory,
        <PERSON>t,
        AuditResult,
        Setting,
        Notification,
      ],
      pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000,
      },
      retry: {
        match: [
          /SQLITE_BUSY/,
          /SQLITE_LOCKED/,
          /ECONNRESET/,
          /SequelizeConnectionError/,
          /SequelizeConnectionRefusedError/,
          /SequelizeHostNotFoundError/,
          /SequelizeHostNotReachableError/,
          /SequelizeInvalidConnectionError/,
          /SequelizeConnectionTimedOutError/,
        ],
        max: 3,
      },
      dialectOptions: config.database.dialect === 'postgres' ? {
        ssl: config.env === 'production' ? {
          require: true,
          rejectUnauthorized: false,
        } : false,
      } : {
        // SQLite options
        timeout: 20000,
      },
      define: {
        timestamps: true,
        underscored: false,
        freezeTableName: true,
      },
    });

    return sequelize;
  }

  private setupEventListeners(): void {
    this.sequelize.addHook('afterConnect', () => {
      loggers.database.connected();
    });

    this.sequelize.addHook('beforeDisconnect', () => {
      loggers.database.disconnected();
    });

    // Handle connection errors
    this.sequelize.addHook('afterConnect', (connection) => {
      if (config.database.dialect === 'postgres') {
        connection.on('error', (error: Error) => {
          loggers.database.error(error.message);
        });
      }
    });
  }

  public async authenticate(): Promise<void> {
    try {
      await this.sequelize.authenticate();
      logger.info('Database connection has been established successfully');
    } catch (error) {
      logger.error('Unable to connect to the database:', error);
      throw error;
    }
  }

  public async sync(options?: { force?: boolean; alter?: boolean }): Promise<void> {
    try {
      await this.sequelize.sync(options);
      logger.info('Database synchronized successfully');
    } catch (error) {
      logger.error('Database synchronization failed:', error);
      throw error;
    }
  }

  public async close(): Promise<void> {
    try {
      await this.sequelize.close();
      logger.info('Database connection closed');
    } catch (error) {
      logger.error('Error closing database connection:', error);
      throw error;
    }
  }

  // Migration helpers
  public async runMigrations(): Promise<void> {
    try {
      const { Umzug, SequelizeStorage } = require('umzug');
      
      const umzug = new Umzug({
        migrations: {
          glob: path.join(__dirname, '../../database/migrations/*.js'),
        },
        context: this.sequelize.getQueryInterface(),
        storage: new SequelizeStorage({ sequelize: this.sequelize }),
        logger: {
          info: (message: string) => logger.info(message),
          warn: (message: string) => logger.warn(message),
          error: (message: string) => logger.error(message),
        },
      });

      const migrations = await umzug.up();
      
      if (migrations.length > 0) {
        logger.info(`Executed ${migrations.length} migrations`);
        migrations.forEach((migration) => {
          loggers.database.migration(migration.name, 'up');
        });
      } else {
        logger.info('No pending migrations');
      }
    } catch (error) {
      logger.error('Migration failed:', error);
      throw error;
    }
  }

  // Health check
  public async healthCheck(): Promise<{ status: 'up' | 'down'; details?: any }> {
    try {
      await this.sequelize.authenticate();
      
      // Test a simple query
      const result = await this.sequelize.query('SELECT 1 as test');
      
      return {
        status: 'up',
        details: {
          dialect: this.sequelize.getDialect(),
          version: this.sequelize.getDatabaseVersion ? await this.sequelize.getDatabaseVersion() : 'unknown',
        },
      };
    } catch (error) {
      return {
        status: 'down',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  // Transaction helper
  public async transaction<T>(callback: (transaction: any) => Promise<T>): Promise<T> {
    const transaction = await this.sequelize.transaction();
    try {
      const result = await callback(transaction);
      await transaction.commit();
      return result;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  // Backup helpers (for SQLite)
  public async backup(backupPath: string): Promise<void> {
    if (config.database.dialect !== 'sqlite') {
      throw new Error('Backup is only supported for SQLite databases');
    }

    try {
      const fs = require('fs').promises;
      const sourcePath = config.database.url.replace('sqlite:', '');
      await fs.copyFile(sourcePath, backupPath);
      logger.info(`Database backed up to ${backupPath}`);
    } catch (error) {
      logger.error('Database backup failed:', error);
      throw error;
    }
  }

  // Cleanup old records
  public async cleanup(): Promise<void> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Clean up old keyword history (keep last 30 days)
      const deletedKeywordHistory = await KeywordHistory.destroy({
        where: {
          createdAt: {
            [this.sequelize.Op.lt]: thirtyDaysAgo,
          },
        },
      });

      // Clean up old notifications (keep last 30 days)
      const deletedNotifications = await Notification.destroy({
        where: {
          createdAt: {
            [this.sequelize.Op.lt]: thirtyDaysAgo,
          },
          read: true,
        },
      });

      logger.info(`Cleanup completed: ${deletedKeywordHistory} keyword history records, ${deletedNotifications} notifications deleted`);
    } catch (error) {
      logger.error('Database cleanup failed:', error);
      throw error;
    }
  }
}

// Create and export database instance
export const database = new Database();
export { Sequelize } from 'sequelize-typescript';
export default database;
