import {
  Table,
  Column,
  <PERSON>,
  <PERSON>Type,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  AllowNull,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Project } from './Project';
import { KeywordHistory } from './KeywordHistory';

export interface KeywordAttributes {
  id: string;
  projectId: string;
  keyword: string;
  currentPosition: number | null;
  previousPosition: number | null;
  bestPosition: number | null;
  worstPosition: number | null;
  url: string | null;
  searchVolume: number | null;
  difficulty: number | null;
  cpc: number | null;
  tags: string[];
  isActive: boolean;
  lastCheckedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface KeywordCreationAttributes extends Omit<KeywordAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

@Table({
  tableName: 'keywords',
  timestamps: true,
  indexes: [
    { fields: ['projectId'] },
    { fields: ['keyword'] },
    { fields: ['currentPosition'] },
    { fields: ['isActive'] },
    { fields: ['lastCheckedAt'] },
    { fields: ['projectId', 'keyword'], unique: true },
  ],
})
export class Keyword extends Model<KeywordAttributes, KeywordCreationAttributes> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Project)
  @AllowNull(false)
  @Column(DataType.UUID)
  projectId!: string;

  @AllowNull(false)
  @Column(DataType.STRING)
  keyword!: string;

  @Column(DataType.INTEGER)
  currentPosition!: number | null;

  @Column(DataType.INTEGER)
  previousPosition!: number | null;

  @Column(DataType.INTEGER)
  bestPosition!: number | null;

  @Column(DataType.INTEGER)
  worstPosition!: number | null;

  @Column(DataType.STRING)
  url!: string | null;

  @Column(DataType.INTEGER)
  searchVolume!: number | null;

  @Column(DataType.FLOAT)
  difficulty!: number | null;

  @Column(DataType.FLOAT)
  cpc!: number | null;

  @Default([])
  @Column(DataType.ARRAY(DataType.STRING))
  tags!: string[];

  @Default(true)
  @Column(DataType.BOOLEAN)
  isActive!: boolean;

  @Column(DataType.DATE)
  lastCheckedAt!: Date | null;

  @CreatedAt
  createdAt!: Date;

  @UpdatedAt
  updatedAt!: Date;

  // Associations
  @BelongsTo(() => Project)
  project!: Project;

  @HasMany(() => KeywordHistory)
  history!: KeywordHistory[];

  // Instance methods
  updatePosition(newPosition: number | null, url?: string | null): void {
    this.previousPosition = this.currentPosition;
    this.currentPosition = newPosition;
    this.lastCheckedAt = new Date();

    if (url) {
      this.url = url;
    }

    // Update best/worst positions
    if (newPosition !== null) {
      if (this.bestPosition === null || newPosition < this.bestPosition) {
        this.bestPosition = newPosition;
      }
      if (this.worstPosition === null || newPosition > this.worstPosition) {
        this.worstPosition = newPosition;
      }
    }
  }

  getPositionChange(): number | null {
    if (this.currentPosition === null || this.previousPosition === null) {
      return null;
    }
    return this.previousPosition - this.currentPosition; // Positive = improvement
  }

  getPositionTrend(): 'up' | 'down' | 'stable' | 'new' {
    const change = this.getPositionChange();
    
    if (change === null) return 'new';
    if (change > 0) return 'up';
    if (change < 0) return 'down';
    return 'stable';
  }

  async addToHistory(): Promise<KeywordHistory> {
    return KeywordHistory.create({
      keywordId: this.id,
      position: this.currentPosition,
      url: this.url,
      checkedAt: this.lastCheckedAt || new Date(),
    });
  }

  // Static methods
  static async findByProjectId(projectId: string, options?: {
    includeInactive?: boolean;
    tags?: string[];
    positionRange?: { min?: number; max?: number };
    limit?: number;
    offset?: number;
  }): Promise<{ keywords: Keyword[]; total: number }> {
    const where: any = { projectId };

    if (!options?.includeInactive) {
      where.isActive = true;
    }

    if (options?.tags && options.tags.length > 0) {
      where.tags = {
        [this.sequelize!.Op.overlap]: options.tags,
      };
    }

    if (options?.positionRange) {
      const positionWhere: any = {};
      if (options.positionRange.min !== undefined) {
        positionWhere[this.sequelize!.Op.gte] = options.positionRange.min;
      }
      if (options.positionRange.max !== undefined) {
        positionWhere[this.sequelize!.Op.lte] = options.positionRange.max;
      }
      if (Object.keys(positionWhere).length > 0) {
        where.currentPosition = positionWhere;
      }
    }

    const { count, rows } = await this.findAndCountAll({
      where,
      include: [
        {
          model: KeywordHistory,
          limit: 30, // Last 30 data points
          order: [['checkedAt', 'DESC']],
        },
      ],
      order: [['keyword', 'ASC']],
      limit: options?.limit,
      offset: options?.offset,
    });

    return { keywords: rows, total: count };
  }

  static async createKeyword(keywordData: {
    projectId: string;
    keyword: string;
    tags?: string[];
    searchVolume?: number;
    difficulty?: number;
    cpc?: number;
  }): Promise<Keyword> {
    return this.create({
      ...keywordData,
      currentPosition: null,
      previousPosition: null,
      bestPosition: null,
      worstPosition: null,
      url: null,
      tags: keywordData.tags || [],
      isActive: true,
      lastCheckedAt: null,
    });
  }

  static async bulkCreateKeywords(
    projectId: string,
    keywords: Array<{
      keyword: string;
      tags?: string[];
      searchVolume?: number;
      difficulty?: number;
      cpc?: number;
    }>
  ): Promise<Keyword[]> {
    const keywordData = keywords.map(k => ({
      ...k,
      projectId,
      currentPosition: null,
      previousPosition: null,
      bestPosition: null,
      worstPosition: null,
      url: null,
      tags: k.tags || [],
      isActive: true,
      lastCheckedAt: null,
    }));

    return this.bulkCreate(keywordData, {
      ignoreDuplicates: true,
    });
  }

  static async updateKeywordPosition(
    keywordId: string,
    position: number | null,
    url?: string | null
  ): Promise<boolean> {
    const keyword = await this.findByPk(keywordId);
    if (!keyword) return false;

    keyword.updatePosition(position, url);
    await keyword.save();
    await keyword.addToHistory();

    return true;
  }

  static async bulkUpdatePositions(
    updates: Array<{
      keywordId: string;
      position: number | null;
      url?: string | null;
    }>
  ): Promise<number> {
    let updatedCount = 0;

    for (const update of updates) {
      const success = await this.updateKeywordPosition(
        update.keywordId,
        update.position,
        update.url
      );
      if (success) updatedCount++;
    }

    return updatedCount;
  }

  static async getKeywordsNeedingUpdate(
    projectId?: string,
    maxAge: number = 24 * 60 * 60 * 1000 // 24 hours in milliseconds
  ): Promise<Keyword[]> {
    const cutoffDate = new Date(Date.now() - maxAge);
    const where: any = {
      isActive: true,
      [this.sequelize!.Op.or]: [
        { lastCheckedAt: null },
        { lastCheckedAt: { [this.sequelize!.Op.lt]: cutoffDate } },
      ],
    };

    if (projectId) {
      where.projectId = projectId;
    }

    return this.findAll({
      where,
      include: [{ model: Project }],
      order: [['lastCheckedAt', 'ASC']],
    });
  }

  static async deactivateKeyword(keywordId: string): Promise<boolean> {
    const [affectedCount] = await this.update(
      { isActive: false },
      { where: { id: keywordId } }
    );
    return affectedCount > 0;
  }

  static async activateKeyword(keywordId: string): Promise<boolean> {
    const [affectedCount] = await this.update(
      { isActive: true },
      { where: { id: keywordId } }
    );
    return affectedCount > 0;
  }

  static async updateTags(keywordId: string, tags: string[]): Promise<boolean> {
    const [affectedCount] = await this.update(
      { tags },
      { where: { id: keywordId } }
    );
    return affectedCount > 0;
  }

  static async getKeywordStats(projectId: string): Promise<{
    total: number;
    active: number;
    topTen: number;
    topThree: number;
    averagePosition: number | null;
    positionDistribution: Record<string, number>;
  }> {
    const keywords = await this.findAll({
      where: { projectId },
      attributes: ['isActive', 'currentPosition'],
    });

    const total = keywords.length;
    const active = keywords.filter(k => k.isActive).length;
    const topTen = keywords.filter(k => k.currentPosition && k.currentPosition <= 10).length;
    const topThree = keywords.filter(k => k.currentPosition && k.currentPosition <= 3).length;

    const positions = keywords
      .map(k => k.currentPosition)
      .filter(p => p !== null && p !== undefined) as number[];

    const averagePosition = positions.length > 0
      ? positions.reduce((sum, pos) => sum + pos, 0) / positions.length
      : null;

    const positionDistribution: Record<string, number> = {
      '1-3': keywords.filter(k => k.currentPosition && k.currentPosition <= 3).length,
      '4-10': keywords.filter(k => k.currentPosition && k.currentPosition >= 4 && k.currentPosition <= 10).length,
      '11-20': keywords.filter(k => k.currentPosition && k.currentPosition >= 11 && k.currentPosition <= 20).length,
      '21-50': keywords.filter(k => k.currentPosition && k.currentPosition >= 21 && k.currentPosition <= 50).length,
      '51+': keywords.filter(k => k.currentPosition && k.currentPosition > 50).length,
      'unranked': keywords.filter(k => k.currentPosition === null).length,
    };

    return {
      total,
      active,
      topTen,
      topThree,
      averagePosition,
      positionDistribution,
    };
  }
}
