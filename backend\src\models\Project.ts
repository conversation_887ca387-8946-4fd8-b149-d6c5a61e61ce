import {
  Table,
  Column,
  Model,
  <PERSON>Type,
  <PERSON><PERSON>ey,
  <PERSON><PERSON><PERSON>,
  AllowNull,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { User } from './User';
import { Keyword } from './Keyword';
import { Audit } from './Audit';

export interface ProjectAttributes {
  id: string;
  userId: string;
  name: string;
  description?: string;
  domain: string;
  country: string;
  language: string;
  searchEngine: 'google' | 'bing' | 'yahoo';
  isActive: boolean;
  settings: {
    notifications: {
      email: boolean;
      webhook: boolean;
      webhookUrl?: string;
    };
    tracking: {
      frequency: 'daily' | 'weekly' | 'monthly';
      autoRefresh: boolean;
      trackMobile: boolean;
      trackDesktop: boolean;
    };
    audits: {
      autoSchedule: boolean;
      frequency: 'weekly' | 'monthly';
      includeSubpages: boolean;
      maxPages: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectCreationAttributes extends Omit<ProjectAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

@Table({
  tableName: 'projects',
  timestamps: true,
  indexes: [
    { fields: ['userId'] },
    { fields: ['domain'] },
    { fields: ['country'] },
    { fields: ['isActive'] },
    { fields: ['userId', 'domain'], unique: true },
  ],
})
export class Project extends Model<ProjectAttributes, ProjectCreationAttributes> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column(DataType.UUID)
  userId!: string;

  @AllowNull(false)
  @Column(DataType.STRING)
  name!: string;

  @Column(DataType.TEXT)
  description?: string;

  @AllowNull(false)
  @Column(DataType.STRING)
  domain!: string;

  @Default('US')
  @Column(DataType.STRING(2))
  country!: string;

  @Default('en')
  @Column(DataType.STRING(5))
  language!: string;

  @Default('google')
  @Column(DataType.ENUM('google', 'bing', 'yahoo'))
  searchEngine!: 'google' | 'bing' | 'yahoo';

  @Default(true)
  @Column(DataType.BOOLEAN)
  isActive!: boolean;

  @Default({
    notifications: {
      email: true,
      webhook: false,
    },
    tracking: {
      frequency: 'daily',
      autoRefresh: true,
      trackMobile: true,
      trackDesktop: true,
    },
    audits: {
      autoSchedule: false,
      frequency: 'weekly',
      includeSubpages: true,
      maxPages: 10,
    },
  })
  @Column(DataType.JSONB)
  settings!: ProjectAttributes['settings'];

  @CreatedAt
  createdAt!: Date;

  @UpdatedAt
  updatedAt!: Date;

  // Associations
  @BelongsTo(() => User)
  user!: User;

  @HasMany(() => Keyword)
  keywords!: Keyword[];

  @HasMany(() => Audit)
  audits!: Audit[];

  // Instance methods
  async getKeywordCount(): Promise<number> {
    return Keyword.count({ where: { projectId: this.id } });
  }

  async getAuditCount(): Promise<number> {
    return Audit.count({ where: { projectId: this.id } });
  }

  async getAveragePosition(): Promise<number | null> {
    const keywords = await Keyword.findAll({
      where: { projectId: this.id },
      attributes: ['currentPosition'],
    });

    const positions = keywords
      .map(k => k.currentPosition)
      .filter(p => p !== null && p !== undefined) as number[];

    if (positions.length === 0) return null;

    return positions.reduce((sum, pos) => sum + pos, 0) / positions.length;
  }

  async updateSettings(newSettings: Partial<ProjectAttributes['settings']>): Promise<void> {
    this.settings = { ...this.settings, ...newSettings };
    await this.save();
  }

  // Static methods
  static async findByUserId(userId: string, options?: { includeInactive?: boolean }): Promise<Project[]> {
    const where: any = { userId };
    
    if (!options?.includeInactive) {
      where.isActive = true;
    }

    return this.findAll({
      where,
      include: [
        {
          model: Keyword,
          required: false,
        },
        {
          model: Audit,
          required: false,
          limit: 5,
          order: [['createdAt', 'DESC']],
        },
      ],
      order: [['createdAt', 'DESC']],
    });
  }

  static async createProject(projectData: {
    userId: string;
    name: string;
    description?: string;
    domain: string;
    country?: string;
    language?: string;
    searchEngine?: 'google' | 'bing' | 'yahoo';
    settings?: Partial<ProjectAttributes['settings']>;
  }): Promise<Project> {
    const defaultSettings: ProjectAttributes['settings'] = {
      notifications: {
        email: true,
        webhook: false,
      },
      tracking: {
        frequency: 'daily',
        autoRefresh: true,
        trackMobile: true,
        trackDesktop: true,
      },
      audits: {
        autoSchedule: false,
        frequency: 'weekly',
        includeSubpages: true,
        maxPages: 10,
      },
    };

    return this.create({
      ...projectData,
      country: projectData.country || 'US',
      language: projectData.language || 'en',
      searchEngine: projectData.searchEngine || 'google',
      isActive: true,
      settings: { ...defaultSettings, ...projectData.settings },
    });
  }

  static async deactivateProject(projectId: string, userId: string): Promise<boolean> {
    const [affectedCount] = await this.update(
      { isActive: false },
      { where: { id: projectId, userId } }
    );
    return affectedCount > 0;
  }

  static async getProjectStats(projectId: string): Promise<{
    keywordCount: number;
    auditCount: number;
    averagePosition: number | null;
    positionChanges: {
      improved: number;
      declined: number;
      unchanged: number;
    };
    lastUpdate: Date | null;
  }> {
    const project = await this.findByPk(projectId, {
      include: [
        {
          model: Keyword,
          include: ['history'],
        },
        {
          model: Audit,
        },
      ],
    });

    if (!project) {
      return {
        keywordCount: 0,
        auditCount: 0,
        averagePosition: null,
        positionChanges: { improved: 0, declined: 0, unchanged: 0 },
        lastUpdate: null,
      };
    }

    const keywordCount = project.keywords.length;
    const auditCount = project.audits.length;
    
    // Calculate average position
    const positions = project.keywords
      .map(k => k.currentPosition)
      .filter(p => p !== null && p !== undefined) as number[];
    
    const averagePosition = positions.length > 0 
      ? positions.reduce((sum, pos) => sum + pos, 0) / positions.length 
      : null;

    // Calculate position changes
    let improved = 0;
    let declined = 0;
    let unchanged = 0;

    for (const keyword of project.keywords) {
      if (keyword.previousPosition !== null && keyword.currentPosition !== null) {
        if (keyword.currentPosition < keyword.previousPosition) {
          improved++;
        } else if (keyword.currentPosition > keyword.previousPosition) {
          declined++;
        } else {
          unchanged++;
        }
      }
    }

    // Find last update
    const lastUpdate = project.keywords.length > 0
      ? project.keywords.reduce((latest, keyword) => {
          return keyword.lastCheckedAt && (!latest || keyword.lastCheckedAt > latest)
            ? keyword.lastCheckedAt
            : latest;
        }, null as Date | null)
      : null;

    return {
      keywordCount,
      auditCount,
      averagePosition,
      positionChanges: { improved, declined, unchanged },
      lastUpdate,
    };
  }
}
