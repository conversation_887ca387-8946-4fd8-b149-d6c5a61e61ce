import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON>Key,
  <PERSON><PERSON>ult,
  AllowNull,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './User';

export interface NotificationAttributes {
  id: string;
  userId: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  data?: Record<string, any>;
  read: boolean;
  readAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationCreationAttributes extends Omit<NotificationAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

@Table({
  tableName: 'notifications',
  timestamps: true,
  indexes: [
    { fields: ['userId'] },
    { fields: ['type'] },
    { fields: ['read'] },
    { fields: ['createdAt'] },
    { fields: ['userId', 'read'] },
  ],
})
export class Notification extends Model<NotificationAttributes, NotificationCreationAttributes> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column(DataType.UUID)
  userId!: string;

  @AllowNull(false)
  @Column(DataType.ENUM('info', 'success', 'warning', 'error'))
  type!: 'info' | 'success' | 'warning' | 'error';

  @AllowNull(false)
  @Column(DataType.STRING)
  title!: string;

  @AllowNull(false)
  @Column(DataType.TEXT)
  message!: string;

  @Column(DataType.JSONB)
  data?: Record<string, any>;

  @Default(false)
  @Column(DataType.BOOLEAN)
  read!: boolean;

  @Column(DataType.DATE)
  readAt!: Date | null;

  @CreatedAt
  createdAt!: Date;

  @UpdatedAt
  updatedAt!: Date;

  // Associations
  @BelongsTo(() => User)
  user!: User;

  // Instance methods
  async markAsRead(): Promise<void> {
    this.read = true;
    this.readAt = new Date();
    await this.save();
  }

  async markAsUnread(): Promise<void> {
    this.read = false;
    this.readAt = null;
    await this.save();
  }

  // Static methods
  static async findByUserId(
    userId: string,
    options?: {
      unreadOnly?: boolean;
      type?: NotificationAttributes['type'];
      limit?: number;
      offset?: number;
    }
  ): Promise<{ notifications: Notification[]; total: number }> {
    const where: any = { userId };

    if (options?.unreadOnly) {
      where.read = false;
    }

    if (options?.type) {
      where.type = options.type;
    }

    const { count, rows } = await this.findAndCountAll({
      where,
      order: [['createdAt', 'DESC']],
      limit: options?.limit,
      offset: options?.offset,
    });

    return { notifications: rows, total: count };
  }

  static async createNotification(notificationData: {
    userId: string;
    type: NotificationAttributes['type'];
    title: string;
    message: string;
    data?: Record<string, any>;
  }): Promise<Notification> {
    return this.create({
      ...notificationData,
      read: false,
      readAt: null,
    });
  }

  static async markAllAsRead(userId: string): Promise<number> {
    const [affectedCount] = await this.update(
      {
        read: true,
        readAt: new Date(),
      },
      {
        where: {
          userId,
          read: false,
        },
      }
    );
    return affectedCount;
  }

  static async getUnreadCount(userId: string): Promise<number> {
    return this.count({
      where: {
        userId,
        read: false,
      },
    });
  }

  static async deleteOldNotifications(daysToKeep: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const deletedCount = await this.destroy({
      where: {
        createdAt: {
          [this.sequelize!.Op.lt]: cutoffDate,
        },
        read: true,
      },
    });

    return deletedCount;
  }

  // Notification templates
  static async notifyKeywordPositionChange(
    userId: string,
    keyword: string,
    oldPosition: number | null,
    newPosition: number | null,
    projectName: string
  ): Promise<Notification> {
    let type: NotificationAttributes['type'] = 'info';
    let title = 'Keyword Position Update';
    let message = '';

    if (oldPosition === null && newPosition !== null) {
      type = 'success';
      title = 'New Keyword Ranking';
      message = `"${keyword}" is now ranking at position ${newPosition} in ${projectName}`;
    } else if (oldPosition !== null && newPosition === null) {
      type = 'warning';
      title = 'Keyword Dropped Out';
      message = `"${keyword}" is no longer ranking in the top 100 for ${projectName}`;
    } else if (oldPosition !== null && newPosition !== null) {
      const change = oldPosition - newPosition;
      if (change > 0) {
        type = 'success';
        title = 'Keyword Improved';
        message = `"${keyword}" improved by ${change} positions (${oldPosition} → ${newPosition}) in ${projectName}`;
      } else if (change < 0) {
        type = 'warning';
        title = 'Keyword Declined';
        message = `"${keyword}" declined by ${Math.abs(change)} positions (${oldPosition} → ${newPosition}) in ${projectName}`;
      } else {
        return this.createNotification({
          userId,
          type: 'info',
          title: 'Keyword Position Stable',
          message: `"${keyword}" maintained position ${newPosition} in ${projectName}`,
          data: { keyword, position: newPosition, projectName },
        });
      }
    }

    return this.createNotification({
      userId,
      type,
      title,
      message,
      data: {
        keyword,
        oldPosition,
        newPosition,
        projectName,
      },
    });
  }

  static async notifyAuditCompleted(
    userId: string,
    auditName: string,
    projectName: string,
    averageScore: number,
    auditId: string
  ): Promise<Notification> {
    const type: NotificationAttributes['type'] = averageScore >= 0.8 ? 'success' : averageScore >= 0.5 ? 'warning' : 'error';
    const scorePercentage = Math.round(averageScore * 100);

    return this.createNotification({
      userId,
      type,
      title: 'Audit Completed',
      message: `"${auditName}" audit completed for ${projectName} with an average score of ${scorePercentage}%`,
      data: {
        auditName,
        projectName,
        averageScore,
        auditId,
      },
    });
  }

  static async notifyAuditFailed(
    userId: string,
    auditName: string,
    projectName: string,
    error: string,
    auditId: string
  ): Promise<Notification> {
    return this.createNotification({
      userId,
      type: 'error',
      title: 'Audit Failed',
      message: `"${auditName}" audit failed for ${projectName}: ${error}`,
      data: {
        auditName,
        projectName,
        error,
        auditId,
      },
    });
  }
}
