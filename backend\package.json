{"name": "seo-saas-backend", "version": "1.0.0", "description": "Backend API for SEO SaaS Platform integrating SerpBear and Lighthouse-Batch", "main": "dist/server.js", "scripts": {"dev": "nodemon --exec \"ts-node -r tsconfig-paths/register src/server.ts\"", "build": "tsc", "start": "node dist/server.js", "worker": "node dist/jobs/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "sequelize-cli db:migrate", "db:migrate:undo": "sequelize-cli db:migrate:undo", "db:seed": "sequelize-cli db:seed:all", "db:reset": "sequelize-cli db:migrate:undo:all && npm run db:migrate && npm run db:seed", "db:create": "sequelize-cli db:create", "db:drop": "sequelize-cli db:drop"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "axios": "^1.10.0", "axios-retry": "^4.0.0", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "bull-board": "^2.1.3", "cheerio": "^1.0.0-rc.12", "chrome-launcher": "^1.1.0", "clsx": "^2.1.1", "compression": "^1.7.4", "connect-redis": "^7.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "cron": "^3.1.6", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "dayjs": "^1.11.10", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lighthouse": "^11.4.0", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.8", "pdf-lib": "^1.17.1", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "puppeteer": "^21.6.1", "react-hook-form": "^7.60.0", "recharts": "^3.0.2", "redis": "^4.6.12", "sequelize": "^6.35.2", "sequelize-typescript": "^2.1.6", "sharp": "^0.33.1", "sqlite3": "^5.1.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "tailwind-merge": "^3.3.1", "tsconfig-paths": "^4.2.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "zod": "^3.25.74"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.17.10", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["seo", "saas", "serpbear", "lighthouse", "keyword-tracking", "seo-audit", "express", "typescript"], "author": "SEO SaaS Platform", "license": "MIT"}