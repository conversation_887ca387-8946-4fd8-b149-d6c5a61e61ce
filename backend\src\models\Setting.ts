import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON>ey,
  Default,
  AllowNull,
  CreatedAt,
  UpdatedAt,
  Unique,
} from 'sequelize-typescript';

export interface SettingAttributes {
  id: string;
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'json' | 'array';
  description?: string;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SettingCreationAttributes extends Omit<SettingAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

@Table({
  tableName: 'settings',
  timestamps: true,
  indexes: [
    { fields: ['key'], unique: true },
    { fields: ['isPublic'] },
  ],
})
export class Setting extends Model<SettingAttributes, SettingCreationAttributes> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @Unique
  @AllowNull(false)
  @Column(DataType.STRING)
  key!: string;

  @AllowNull(false)
  @Column(DataType.JSONB)
  value!: any;

  @Default('string')
  @Column(DataType.ENUM('string', 'number', 'boolean', 'json', 'array'))
  type!: 'string' | 'number' | 'boolean' | 'json' | 'array';

  @Column(DataType.TEXT)
  description?: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  isPublic!: boolean;

  @CreatedAt
  createdAt!: Date;

  @UpdatedAt
  updatedAt!: Date;

  // Instance methods
  getValue(): any {
    switch (this.type) {
      case 'string':
        return String(this.value);
      case 'number':
        return Number(this.value);
      case 'boolean':
        return Boolean(this.value);
      case 'json':
      case 'array':
        return this.value;
      default:
        return this.value;
    }
  }

  // Static methods
  static async getSetting(key: string, defaultValue?: any): Promise<any> {
    const setting = await this.findOne({ where: { key } });
    return setting ? setting.getValue() : defaultValue;
  }

  static async setSetting(
    key: string,
    value: any,
    type: SettingAttributes['type'] = 'string',
    options?: {
      description?: string;
      isPublic?: boolean;
    }
  ): Promise<Setting> {
    const [setting] = await this.upsert({
      key,
      value,
      type,
      description: options?.description,
      isPublic: options?.isPublic || false,
    });
    return setting;
  }

  static async getPublicSettings(): Promise<Record<string, any>> {
    const settings = await this.findAll({
      where: { isPublic: true },
      attributes: ['key', 'value', 'type'],
    });

    const result: Record<string, any> = {};
    settings.forEach(setting => {
      result[setting.key] = setting.getValue();
    });

    return result;
  }

  static async getAllSettings(): Promise<Record<string, any>> {
    const settings = await this.findAll({
      attributes: ['key', 'value', 'type'],
    });

    const result: Record<string, any> = {};
    settings.forEach(setting => {
      result[setting.key] = setting.getValue();
    });

    return result;
  }

  static async deleteSetting(key: string): Promise<boolean> {
    const deletedCount = await this.destroy({ where: { key } });
    return deletedCount > 0;
  }

  static async initializeDefaultSettings(): Promise<void> {
    const defaultSettings = [
      {
        key: 'app.name',
        value: 'SEO SaaS Platform',
        type: 'string' as const,
        description: 'Application name',
        isPublic: true,
      },
      {
        key: 'app.version',
        value: '1.0.0',
        type: 'string' as const,
        description: 'Application version',
        isPublic: true,
      },
      {
        key: 'features.registration',
        value: true,
        type: 'boolean' as const,
        description: 'Enable user registration',
        isPublic: true,
      },
      {
        key: 'features.email_notifications',
        value: true,
        type: 'boolean' as const,
        description: 'Enable email notifications',
        isPublic: false,
      },
      {
        key: 'limits.max_projects_per_user',
        value: 10,
        type: 'number' as const,
        description: 'Maximum projects per user',
        isPublic: false,
      },
      {
        key: 'limits.max_keywords_per_project',
        value: 1000,
        type: 'number' as const,
        description: 'Maximum keywords per project',
        isPublic: false,
      },
      {
        key: 'serp.default_country',
        value: 'US',
        type: 'string' as const,
        description: 'Default country for SERP tracking',
        isPublic: false,
      },
      {
        key: 'serp.default_language',
        value: 'en',
        type: 'string' as const,
        description: 'Default language for SERP tracking',
        isPublic: false,
      },
      {
        key: 'lighthouse.default_device',
        value: 'desktop',
        type: 'string' as const,
        description: 'Default device for Lighthouse audits',
        isPublic: false,
      },
      {
        key: 'lighthouse.default_categories',
        value: ['performance', 'accessibility', 'best-practices', 'seo', 'pwa'],
        type: 'array' as const,
        description: 'Default categories for Lighthouse audits',
        isPublic: false,
      },
    ];

    for (const setting of defaultSettings) {
      await this.setSetting(
        setting.key,
        setting.value,
        setting.type,
        {
          description: setting.description,
          isPublic: setting.isPublic,
        }
      );
    }
  }
}
