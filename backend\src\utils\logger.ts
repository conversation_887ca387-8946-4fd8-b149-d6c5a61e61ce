import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import { config } from './config';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define format for logs
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Define format for file logs (without colors)
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Define which transports the logger must use
const transports: winston.transport[] = [
  // Console transport
  new winston.transports.Console({
    format,
    level: config.env === 'development' ? 'debug' : 'info',
  }),
];

// Add file transports if enabled
if (config.logging.toFile) {
  // Ensure logs directory exists
  const logsDir = path.dirname(config.logging.filePath);
  
  // Error logs
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      format: fileFormat,
      maxSize: '20m',
      maxFiles: '14d',
      zippedArchive: true,
    })
  );

  // Combined logs
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'combined-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      format: fileFormat,
      maxSize: '20m',
      maxFiles: '14d',
      zippedArchive: true,
    })
  );

  // HTTP logs
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'http-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: 'http',
      format: fileFormat,
      maxSize: '20m',
      maxFiles: '7d',
      zippedArchive: true,
    })
  );
}

// Create the logger
export const logger = winston.createLogger({
  level: config.logging.level,
  levels,
  format: fileFormat,
  transports,
  exitOnError: false,
});

// Create a stream object for Morgan HTTP logging
export const morganStream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Helper functions for structured logging
export const loggers = {
  // Authentication logs
  auth: {
    login: (userId: string, ip: string) => {
      logger.info('User login', { userId, ip, action: 'login' });
    },
    logout: (userId: string, ip: string) => {
      logger.info('User logout', { userId, ip, action: 'logout' });
    },
    register: (userId: string, email: string, ip: string) => {
      logger.info('User registration', { userId, email, ip, action: 'register' });
    },
    loginFailed: (email: string, ip: string, reason: string) => {
      logger.warn('Login failed', { email, ip, reason, action: 'login_failed' });
    },
  },

  // Keyword tracking logs
  keywords: {
    refresh: (keywordId: string, position: number | null, previousPosition: number | null) => {
      logger.info('Keyword position updated', { 
        keywordId, 
        position, 
        previousPosition, 
        action: 'keyword_refresh' 
      });
    },
    bulkRefresh: (projectId: string, count: number, duration: number) => {
      logger.info('Bulk keyword refresh completed', { 
        projectId, 
        count, 
        duration, 
        action: 'bulk_keyword_refresh' 
      });
    },
    error: (keywordId: string, error: string) => {
      logger.error('Keyword refresh failed', { keywordId, error, action: 'keyword_error' });
    },
  },

  // Audit logs
  audits: {
    started: (auditId: string, projectId: string, urlCount: number) => {
      logger.info('Audit started', { auditId, projectId, urlCount, action: 'audit_started' });
    },
    completed: (auditId: string, duration: number, averageScore: number) => {
      logger.info('Audit completed', { 
        auditId, 
        duration, 
        averageScore, 
        action: 'audit_completed' 
      });
    },
    failed: (auditId: string, error: string) => {
      logger.error('Audit failed', { auditId, error, action: 'audit_failed' });
    },
    urlProcessed: (auditId: string, url: string, scores: any) => {
      logger.debug('Audit URL processed', { auditId, url, scores, action: 'audit_url_processed' });
    },
  },

  // SERP API logs
  serp: {
    request: (provider: string, keyword: string, country: string) => {
      logger.debug('SERP API request', { provider, keyword, country, action: 'serp_request' });
    },
    response: (provider: string, keyword: string, position: number | null, duration: number) => {
      logger.debug('SERP API response', { 
        provider, 
        keyword, 
        position, 
        duration, 
        action: 'serp_response' 
      });
    },
    error: (provider: string, keyword: string, error: string) => {
      logger.error('SERP API error', { provider, keyword, error, action: 'serp_error' });
    },
    rateLimited: (provider: string, retryAfter: number) => {
      logger.warn('SERP API rate limited', { provider, retryAfter, action: 'serp_rate_limited' });
    },
  },

  // Email logs
  email: {
    sent: (to: string, subject: string, type: string) => {
      logger.info('Email sent', { to, subject, type, action: 'email_sent' });
    },
    failed: (to: string, subject: string, error: string) => {
      logger.error('Email failed', { to, subject, error, action: 'email_failed' });
    },
  },

  // Database logs
  database: {
    connected: () => {
      logger.info('Database connected', { action: 'database_connected' });
    },
    disconnected: () => {
      logger.warn('Database disconnected', { action: 'database_disconnected' });
    },
    error: (error: string) => {
      logger.error('Database error', { error, action: 'database_error' });
    },
    migration: (migration: string, direction: 'up' | 'down') => {
      logger.info('Database migration', { migration, direction, action: 'database_migration' });
    },
  },

  // Job queue logs
  jobs: {
    started: (jobId: string, type: string, data: any) => {
      logger.info('Job started', { jobId, type, data, action: 'job_started' });
    },
    completed: (jobId: string, type: string, duration: number) => {
      logger.info('Job completed', { jobId, type, duration, action: 'job_completed' });
    },
    failed: (jobId: string, type: string, error: string, attempts: number) => {
      logger.error('Job failed', { jobId, type, error, attempts, action: 'job_failed' });
    },
    retry: (jobId: string, type: string, attempt: number, maxAttempts: number) => {
      logger.warn('Job retry', { jobId, type, attempt, maxAttempts, action: 'job_retry' });
    },
  },

  // Security logs
  security: {
    suspiciousActivity: (ip: string, userAgent: string, action: string) => {
      logger.warn('Suspicious activity detected', { 
        ip, 
        userAgent, 
        action: 'suspicious_activity',
        details: action 
      });
    },
    rateLimitExceeded: (ip: string, endpoint: string) => {
      logger.warn('Rate limit exceeded', { ip, endpoint, action: 'rate_limit_exceeded' });
    },
    invalidToken: (ip: string, token: string) => {
      logger.warn('Invalid token used', { ip, token: token.substring(0, 10), action: 'invalid_token' });
    },
  },
};

export default logger;
