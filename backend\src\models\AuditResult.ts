import {
  Table,
  Column,
  Model,
  <PERSON>Type,
  <PERSON><PERSON>ey,
  <PERSON><PERSON><PERSON>,
  AllowNull,
  CreatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Audit } from './Audit';

export interface AuditResultAttributes {
  id: string;
  auditId: string;
  url: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  scores: {
    performance: number;
    accessibility: number;
    bestPractices: number;
    seo: number;
    pwa: number;
  };
  metrics: {
    firstContentfulPaint: number;
    largestContentfulPaint: number;
    firstInputDelay: number;
    cumulativeLayoutShift: number;
    speedIndex: number;
    totalBlockingTime: number;
  };
  opportunities: Array<{
    id: string;
    title: string;
    description: string;
    score: number;
    numericValue: number;
    displayValue: string;
  }>;
  diagnostics: Array<{
    id: string;
    title: string;
    description: string;
    score: number;
    displayValue: string;
  }>;
  reportPath: string | null;
  error?: string;
  duration: number | null;
  createdAt: Date;
}

export interface AuditResultCreationAttributes extends Omit<AuditResultAttributes, 'id' | 'createdAt'> {}

@Table({
  tableName: 'audit_results',
  timestamps: false,
  indexes: [
    { fields: ['auditId'] },
    { fields: ['url'] },
    { fields: ['status'] },
    { fields: ['auditId', 'url'], unique: true },
  ],
})
export class AuditResult extends Model<AuditResultAttributes, AuditResultCreationAttributes> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Audit)
  @AllowNull(false)
  @Column(DataType.UUID)
  auditId!: string;

  @AllowNull(false)
  @Column(DataType.STRING)
  url!: string;

  @Default('pending')
  @Column(DataType.ENUM('pending', 'running', 'completed', 'failed'))
  status!: 'pending' | 'running' | 'completed' | 'failed';

  @Default({
    performance: 0,
    accessibility: 0,
    bestPractices: 0,
    seo: 0,
    pwa: 0,
  })
  @Column(DataType.JSONB)
  scores!: AuditResultAttributes['scores'];

  @Default({
    firstContentfulPaint: 0,
    largestContentfulPaint: 0,
    firstInputDelay: 0,
    cumulativeLayoutShift: 0,
    speedIndex: 0,
    totalBlockingTime: 0,
  })
  @Column(DataType.JSONB)
  metrics!: AuditResultAttributes['metrics'];

  @Default([])
  @Column(DataType.JSONB)
  opportunities!: AuditResultAttributes['opportunities'];

  @Default([])
  @Column(DataType.JSONB)
  diagnostics!: AuditResultAttributes['diagnostics'];

  @Column(DataType.STRING)
  reportPath!: string | null;

  @Column(DataType.TEXT)
  error?: string;

  @Column(DataType.INTEGER)
  duration!: number | null;

  @CreatedAt
  createdAt!: Date;

  // Associations
  @BelongsTo(() => Audit)
  audit!: Audit;

  // Instance methods
  getOverallScore(): number {
    const scores = [
      this.scores.performance,
      this.scores.accessibility,
      this.scores.bestPractices,
      this.scores.seo,
      this.scores.pwa,
    ];
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }

  getScoreGrade(score: number): 'good' | 'needs-improvement' | 'poor' {
    if (score >= 0.9) return 'good';
    if (score >= 0.5) return 'needs-improvement';
    return 'poor';
  }

  getCriticalIssues(): Array<AuditResultAttributes['opportunities'][0] | AuditResultAttributes['diagnostics'][0]> {
    const criticalOpportunities = this.opportunities.filter(opp => opp.score < 0.5);
    const criticalDiagnostics = this.diagnostics.filter(diag => diag.score < 0.5);
    return [...criticalOpportunities, ...criticalDiagnostics];
  }

  // Static methods
  static async findByAuditId(auditId: string): Promise<AuditResult[]> {
    return this.findAll({
      where: { auditId },
      order: [['url', 'ASC']],
    });
  }

  static async createResult(resultData: {
    auditId: string;
    url: string;
  }): Promise<AuditResult> {
    return this.create({
      ...resultData,
      status: 'pending',
      scores: {
        performance: 0,
        accessibility: 0,
        bestPractices: 0,
        seo: 0,
        pwa: 0,
      },
      metrics: {
        firstContentfulPaint: 0,
        largestContentfulPaint: 0,
        firstInputDelay: 0,
        cumulativeLayoutShift: 0,
        speedIndex: 0,
        totalBlockingTime: 0,
      },
      opportunities: [],
      diagnostics: [],
      reportPath: null,
      duration: null,
    });
  }

  static async updateResult(
    resultId: string,
    data: {
      status: 'completed' | 'failed';
      scores?: AuditResultAttributes['scores'];
      metrics?: AuditResultAttributes['metrics'];
      opportunities?: AuditResultAttributes['opportunities'];
      diagnostics?: AuditResultAttributes['diagnostics'];
      reportPath?: string;
      error?: string;
      duration?: number;
    }
  ): Promise<boolean> {
    const [affectedCount] = await this.update(data, {
      where: { id: resultId },
    });
    return affectedCount > 0;
  }

  static async getResultsByUrl(url: string, limit: number = 10): Promise<AuditResult[]> {
    return this.findAll({
      where: { url, status: 'completed' },
      include: [{ model: Audit }],
      order: [['createdAt', 'DESC']],
      limit,
    });
  }

  static async getAverageScoresForProject(projectId: string): Promise<AuditResultAttributes['scores'] | null> {
    const results = await this.findAll({
      where: { status: 'completed' },
      include: [
        {
          model: Audit,
          where: { projectId },
          required: true,
        },
      ],
      attributes: ['scores'],
    });

    if (results.length === 0) return null;

    const totals = {
      performance: 0,
      accessibility: 0,
      bestPractices: 0,
      seo: 0,
      pwa: 0,
    };

    results.forEach(result => {
      totals.performance += result.scores.performance;
      totals.accessibility += result.scores.accessibility;
      totals.bestPractices += result.scores.bestPractices;
      totals.seo += result.scores.seo;
      totals.pwa += result.scores.pwa;
    });

    const count = results.length;
    return {
      performance: Math.round((totals.performance / count) * 100) / 100,
      accessibility: Math.round((totals.accessibility / count) * 100) / 100,
      bestPractices: Math.round((totals.bestPractices / count) * 100) / 100,
      seo: Math.round((totals.seo / count) * 100) / 100,
      pwa: Math.round((totals.pwa / count) * 100) / 100,
    };
  }
}
