import axios, { AxiosInstance, AxiosResponse } from 'axios';

// Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user';
  isEmailVerified: boolean;
  settings: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface Project {
  id: string;
  name: string;
  domain: string;
  description?: string;
  settings: Record<string, any>;
  userId: string;
  createdAt: string;
  updatedAt: string;
  keywordCount?: number;
  auditCount?: number;
}

export interface Keyword {
  id: string;
  keyword: string;
  targetUrl: string;
  currentPosition: number | null;
  previousPosition: number | null;
  bestPosition: number | null;
  searchVolume: number | null;
  difficulty: number | null;
  tags: string[];
  isActive: boolean;
  projectId: string;
  createdAt: string;
  updatedAt: string;
  lastChecked?: string;
}

export interface KeywordHistory {
  id: string;
  position: number;
  url: string;
  title: string;
  snippet: string;
  keywordId: string;
  createdAt: string;
}

export interface Audit {
  id: string;
  name: string;
  urls: string[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  settings: Record<string, any>;
  projectId: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  resultCount?: number;
}

export interface AuditResult {
  id: string;
  url: string;
  performanceScore: number;
  accessibilityScore: number;
  bestPracticesScore: number;
  seoScore: number;
  metrics: Record<string, any>;
  opportunities: any[];
  diagnostics: any[];
  auditId: string;
  createdAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  token: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async login(data: LoginRequest): Promise<AuthResponse> {
    const response = await this.client.post<ApiResponse<AuthResponse>>('/auth/login', data);
    return response.data.data;
  }

  async register(data: RegisterRequest): Promise<AuthResponse> {
    const response = await this.client.post<ApiResponse<AuthResponse>>('/auth/register', data);
    return response.data.data;
  }

  async logout(): Promise<void> {
    await this.client.post('/auth/logout');
    localStorage.removeItem('token');
  }

  async getProfile(): Promise<User> {
    const response = await this.client.get<ApiResponse<User>>('/auth/profile');
    return response.data.data;
  }

  // Project endpoints
  async getProjects(): Promise<Project[]> {
    const response = await this.client.get<PaginatedResponse<Project>>('/projects');
    return response.data.data;
  }

  async getProject(id: string): Promise<Project> {
    const response = await this.client.get<ApiResponse<Project>>(`/projects/${id}`);
    return response.data.data;
  }

  async createProject(data: Partial<Project>): Promise<Project> {
    const response = await this.client.post<ApiResponse<Project>>('/projects', data);
    return response.data.data;
  }

  async updateProject(id: string, data: Partial<Project>): Promise<Project> {
    const response = await this.client.put<ApiResponse<Project>>(`/projects/${id}`, data);
    return response.data.data;
  }

  async deleteProject(id: string): Promise<void> {
    await this.client.delete(`/projects/${id}`);
  }

  // Keyword endpoints
  async getKeywords(projectId: string, params?: any): Promise<PaginatedResponse<Keyword>> {
    const response = await this.client.get<PaginatedResponse<Keyword>>(`/keywords`, {
      params: { projectId, ...params }
    });
    return response.data;
  }

  async getKeyword(id: string): Promise<Keyword> {
    const response = await this.client.get<ApiResponse<Keyword>>(`/keywords/${id}`);
    return response.data.data;
  }

  async createKeyword(data: Partial<Keyword>): Promise<Keyword> {
    const response = await this.client.post<ApiResponse<Keyword>>('/keywords', data);
    return response.data.data;
  }

  async updateKeyword(id: string, data: Partial<Keyword>): Promise<Keyword> {
    const response = await this.client.put<ApiResponse<Keyword>>(`/keywords/${id}`, data);
    return response.data.data;
  }

  async deleteKeyword(id: string): Promise<void> {
    await this.client.delete(`/keywords/${id}`);
  }

  async bulkUpdateKeywords(projectId: string): Promise<void> {
    await this.client.post(`/keywords/bulk-update`, { projectId });
  }

  // Audit endpoints
  async getAudits(projectId: string): Promise<Audit[]> {
    const response = await this.client.get<PaginatedResponse<Audit>>('/audits', {
      params: { projectId }
    });
    return response.data.data;
  }

  async getAudit(id: string): Promise<Audit> {
    const response = await this.client.get<ApiResponse<Audit>>(`/audits/${id}`);
    return response.data.data;
  }

  async createAudit(data: Partial<Audit>): Promise<Audit> {
    const response = await this.client.post<ApiResponse<Audit>>('/audits', data);
    return response.data.data;
  }

  async getAuditResults(auditId: string): Promise<AuditResult[]> {
    const response = await this.client.get<PaginatedResponse<AuditResult>>(`/audits/${auditId}/results`);
    return response.data.data;
  }

  // Dashboard endpoints
  async getDashboardOverview(): Promise<any> {
    const response = await this.client.get<ApiResponse<any>>('/dashboard/overview');
    return response.data.data;
  }

  async getDashboardTrends(projectId?: string): Promise<any> {
    const response = await this.client.get<ApiResponse<any>>('/dashboard/trends', {
      params: { projectId }
    });
    return response.data.data;
  }
}

export const api = new ApiClient();
export default api;
