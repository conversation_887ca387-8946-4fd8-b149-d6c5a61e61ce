'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { api } from '@/lib/api';
import { formatNumber, formatPercentage } from '@/lib/utils';
import {
  ChartBarIcon,
  MagnifyingGlassIcon,
  DocumentMagnifyingGlassIcon,
  TrendingUpIcon,
  TrendingDownIcon,
} from '@heroicons/react/24/outline';

interface DashboardData {
  overview: {
    totalProjects: number;
    totalKeywords: number;
    totalAudits: number;
    avgPosition: number;
    positionChange: number;
    avgScore: number;
    scoreChange: number;
  };
  recentKeywords: Array<{
    id: string;
    keyword: string;
    currentPosition: number;
    previousPosition: number;
    change: number;
  }>;
  recentAudits: Array<{
    id: string;
    name: string;
    status: string;
    avgScore: number;
    createdAt: string;
  }>;
}

export default function DashboardPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
      return;
    }

    if (user) {
      loadDashboardData();
    }
  }, [user, loading, router]);

  const loadDashboardData = async () => {
    try {
      const data = await api.getDashboardOverview();
      setDashboardData(data);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (loading || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!dashboardData) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Welcome to SEO SaaS!</h3>
            <p className="text-gray-600 mb-6">Get started by creating your first project.</p>
            <button
              onClick={() => router.push('/projects')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Create Project
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const { overview, recentKeywords, recentAudits } = dashboardData;

  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Overview of your SEO performance</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Projects</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(overview.totalProjects)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <MagnifyingGlassIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Keywords</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(overview.totalKeywords)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentMagnifyingGlassIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Audits</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(overview.totalAudits)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                {overview.positionChange >= 0 ? (
                  <TrendingUpIcon className="h-8 w-8 text-green-600" />
                ) : (
                  <TrendingDownIcon className="h-8 w-8 text-red-600" />
                )}
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Position</p>
                <p className="text-2xl font-bold text-gray-900">{overview.avgPosition.toFixed(1)}</p>
                <p className={`text-sm ${overview.positionChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {overview.positionChange >= 0 ? '+' : ''}{overview.positionChange.toFixed(1)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Keywords */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Keywords</h3>
            </div>
            <div className="p-6">
              {recentKeywords.length > 0 ? (
                <div className="space-y-4">
                  {recentKeywords.map((keyword) => (
                    <div key={keyword.id} className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{keyword.keyword}</p>
                        <p className="text-sm text-gray-600">Position: {keyword.currentPosition}</p>
                      </div>
                      <div className="text-right">
                        <div className={`flex items-center ${
                          keyword.change > 0 ? 'text-green-600' : 
                          keyword.change < 0 ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {keyword.change > 0 && <TrendingUpIcon className="h-4 w-4 mr-1" />}
                          {keyword.change < 0 && <TrendingDownIcon className="h-4 w-4 mr-1" />}
                          <span className="text-sm font-medium">
                            {keyword.change > 0 ? '+' : ''}{keyword.change}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600 text-center py-4">No keywords tracked yet</p>
              )}
            </div>
          </div>

          {/* Recent Audits */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Audits</h3>
            </div>
            <div className="p-6">
              {recentAudits.length > 0 ? (
                <div className="space-y-4">
                  {recentAudits.map((audit) => (
                    <div key={audit.id} className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{audit.name}</p>
                        <p className="text-sm text-gray-600">
                          Status: <span className={`capitalize ${
                            audit.status === 'completed' ? 'text-green-600' :
                            audit.status === 'running' ? 'text-blue-600' :
                            audit.status === 'failed' ? 'text-red-600' : 'text-gray-600'
                          }`}>{audit.status}</span>
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {formatPercentage(audit.avgScore)}
                        </p>
                        <p className="text-xs text-gray-600">
                          {new Date(audit.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600 text-center py-4">No audits run yet</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
