# SEO SaaS Frontend

A modern Next.js frontend for the SEO SaaS platform that combines keyword rank tracking and technical SEO audits.

## Features

- **Authentication System**: Complete login/register with JWT tokens
- **Dashboard**: Overview of projects, keywords, and audit performance
- **Project Management**: Create and manage SEO projects
- **Keyword Tracking**: Monitor keyword rankings with position changes
- **SEO Audits**: Technical SEO audits powered by Lighthouse
- **Settings**: User profile, password, notifications, and preferences
- **Responsive Design**: Mobile-first responsive layout
- **Real-time Updates**: Live data updates and notifications

## Tech Stack

- **Framework**: Next.js 15.3.5 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4.0
- **UI Components**: Headless UI, Heroicons
- **Forms**: React Hook Form with Zod validation
- **HTTP Client**: Axios with interceptors
- **Charts**: Recharts for data visualization
- **State Management**: React Context API

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Backend API running on port 3001

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd frontend-temp
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXT_PUBLIC_APP_NAME="SEO SaaS"
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development
```

4. Start the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── dashboard/         # Dashboard page
│   ├── projects/          # Projects management
│   ├── keywords/          # Keywords tracking
│   ├── audits/           # SEO audits
│   ├── settings/         # User settings
│   ├── login/            # Authentication pages
│   ├── register/
│   ├── layout.tsx        # Root layout
│   ├── page.tsx          # Landing page
│   └── globals.css       # Global styles
├── components/
│   ├── layout/           # Layout components
│   │   └── DashboardLayout.tsx
│   └── providers/        # Context providers
│       └── AuthProvider.tsx
├── lib/
│   ├── api.ts           # API client and types
│   └── utils.ts         # Utility functions
└── middleware.ts        # Route protection
```

## Key Components

### Authentication Provider
- JWT token management
- User state management
- Automatic token refresh
- Route protection

### Dashboard Layout
- Responsive sidebar navigation
- Mobile hamburger menu
- User profile display
- Logout functionality

### API Client
- Axios instance with interceptors
- TypeScript interfaces
- Error handling
- Token management

### Utility Functions
- Date formatting
- Number formatting
- Position change calculations
- Score color helpers

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NEXT_PUBLIC_API_URL` | Backend API URL | `http://localhost:3001/api` |
| `NEXT_PUBLIC_APP_NAME` | Application name | `SEO SaaS` |
| `NEXT_PUBLIC_APP_URL` | Frontend URL | `http://localhost:3000` |
| `NODE_ENV` | Environment | `development` |

## API Integration

The frontend integrates with the backend API for:

- **Authentication**: Login, register, profile management
- **Projects**: CRUD operations for SEO projects
- **Keywords**: Keyword tracking and bulk operations
- **Audits**: SEO audit creation and results viewing
- **Dashboard**: Overview statistics and recent activity

## Deployment

### Vercel (Recommended)

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment

1. Build the application:
```bash
npm run build
```

2. Start the production server:
```bash
npm start
```

## Features Overview

### Dashboard
- Project, keyword, and audit statistics
- Recent activity feeds
- Quick action buttons
- Performance metrics

### Projects
- Create and manage SEO projects
- Project statistics and metrics
- Domain and description management

### Keywords
- Bulk keyword import
- Position tracking and history
- Tag-based organization
- Search and filtering

### Audits
- Lighthouse-powered SEO audits
- Performance, SEO, accessibility scores
- Desktop and mobile auditing
- Historical audit results

### Settings
- Profile management
- Password updates
- Notification preferences
- Application preferences

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
