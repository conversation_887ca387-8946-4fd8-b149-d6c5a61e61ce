# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.19](https://github.com/inspect-js/which-typed-array/compare/v1.1.18...v1.1.19) - 2025-03-08

### Commits

- [Refactor] use `get-proto`, improve types [`e05d535`](https://github.com/inspect-js/which-typed-array/commit/e05d535fe4e4c4e674937718fe1cae90abff3606)
- [Dev <PERSON><PERSON>] update `@arethetypeswrong/cli`, `@ljharb/tsconfig`, `@types/tape` [`0dade9c`](https://github.com/inspect-js/which-typed-array/commit/0dade9c4c334f37ed14083a35724eea56a496991)
- [Deps] update `call-bound`, `for-each` [`490791a`](https://github.com/inspect-js/which-typed-array/commit/490791af49605390f9805660492976f86c64feb1)
- [Tests] skip `npm ls` in older nodes [`f83aaca`](https://github.com/inspect-js/which-typed-array/commit/f83aaca6b6634ce795f8caf9a1e14ab15d35161c)
- [Dev Deps] update `@ljharb/tsconfig` [`63c4795`](https://github.com/inspect-js/which-typed-array/commit/63c479564e5f3cb022c784ffe505673597341aab)

## [v1.1.18](https://github.com/inspect-js/which-typed-array/compare/v1.1.17...v1.1.18) - 2024-12-18

### Commits

- [types] improve types [`4b57173`](https://github.com/inspect-js/which-typed-array/commit/4b5717349976578c6b48966d581687df5dcc2e9b)
- [Dev Deps] update `@types/tape` [`81853b0`](https://github.com/inspect-js/which-typed-array/commit/81853b075c018538859a5533578be654fafecdae)

## [v1.1.17](https://github.com/inspect-js/which-typed-array/compare/v1.1.16...v1.1.17) - 2024-12-18

### Commits

- [types] improve types [`86bc612`](https://github.com/inspect-js/which-typed-array/commit/86bc61207e5970c2c7e13cdda4ccdeb0981ac40b)
- [Dev Deps] update `@arethetypeswrong/cli`, `@ljharb/tsconfig`, `@types/tape` [`2e9bed6`](https://github.com/inspect-js/which-typed-array/commit/2e9bed67f1d623b176b1a7f06c5eab006c21cf96)
- [Deps] update `call-bind`, `gopd` [`34579df`](https://github.com/inspect-js/which-typed-array/commit/34579df639e35ceb3a7e54f8e680a4077a950b8b)
- [Refactor] use `call-bound` directly [`2a2d84e`](https://github.com/inspect-js/which-typed-array/commit/2a2d84e91045266841ddb47afe594899bae2f483)

## [v1.1.16](https://github.com/inspect-js/which-typed-array/compare/v1.1.15...v1.1.16) - 2024-11-27

### Commits

- [actions] split out node 10-20, and 20+ [`8e289a9`](https://github.com/inspect-js/which-typed-array/commit/8e289a9665a32f7ea267c3ffed7451b154adbe26)
- [Dev Deps] update `@arethetypeswrong/cli`, `@ljharb/eslint-config`, `@types/node`, `@types/tape`, `auto-changelog`, `tape` [`3d4a678`](https://github.com/inspect-js/which-typed-array/commit/3d4a67872d0dbecb755e63ba4101e9ec030a5e7e)
- [Tests] replace `aud` with `npm audit` [`6fbada9`](https://github.com/inspect-js/which-typed-array/commit/6fbada976743192db47000e47eefc07708713ea0)
- [types] add an additional overload [`db5a791`](https://github.com/inspect-js/which-typed-array/commit/db5a791642cd8b4d78fe4ed4da151c4543ee0840)
- [Dev Deps] remove an unused DT package [`6bfff4c`](https://github.com/inspect-js/which-typed-array/commit/6bfff4c3b0c415cb32cd12be6fab3cbbe9e10e13)
- [Dev Deps] add missing peer dep [`05fd582`](https://github.com/inspect-js/which-typed-array/commit/05fd582a703cd68ee7613af0ef2c45546ea5d2ba)

## [v1.1.15](https://github.com/inspect-js/which-typed-array/compare/v1.1.14...v1.1.15) - 2024-03-10

### Commits

- [types] use a namespace; improve type [`f42bec3`](https://github.com/inspect-js/which-typed-array/commit/f42bec34d5c47bd9e4ab1b48dcde60c09c666712)
- [types] use shared config [`464a9e3`](https://github.com/inspect-js/which-typed-array/commit/464a9e358c2597253c747970b12032406a19b8d2)
- [actions] remove redundant finisher; use reusable workflow [`d114ee8`](https://github.com/inspect-js/which-typed-array/commit/d114ee83ceb6c7898386f4b5935a3ed9e2ec61e4)
- [Dev Deps] update `@types/node`, `tape`, `typescript`; add `@arethetypeswrong/cli` [`9cc63d8`](https://github.com/inspect-js/which-typed-array/commit/9cc63d8635e80ce6dabcb352d23050111040d747)
- [types] add a helpful hover description [`29ccf8d`](https://github.com/inspect-js/which-typed-array/commit/29ccf8dab0f805cdac6ec56d7b9cc27476708273)
- [Deps] update `available-typed-arrays`, `call-bind`, `has-tostringtag` [`7ecfd8e`](https://github.com/inspect-js/which-typed-array/commit/7ecfd8e29d09f8708f7cab7cc41fea9ae5a20867)

## [v1.1.14](https://github.com/inspect-js/which-typed-array/compare/v1.1.13...v1.1.14) - 2024-02-01

### Commits

- [patch] add types [`49c4d4c`](https://github.com/inspect-js/which-typed-array/commit/49c4d4c5db9bebb8d6f8c18a01047e44eea15e17)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`e5fab7b`](https://github.com/inspect-js/which-typed-array/commit/e5fab7b3dc9df2bceb88f15c3d0a2c0176cf2567)
- [Deps] update `available-typed-arrays`, `call-bind` [`97e2b44`](https://github.com/inspect-js/which-typed-array/commit/97e2b44bad85c9183f1219e28211b3abd167677c)
- [Deps] update `has-tostringtag` [`1efa8bf`](https://github.com/inspect-js/which-typed-array/commit/1efa8bf910c080c14f011aa7c645ac88bc7a7078)

## [v1.1.13](https://github.com/inspect-js/which-typed-array/compare/v1.1.12...v1.1.13) - 2023-10-19

### Commits

- [Refactor] avoid call-binding entirely when there is no method to bind [`9ff452b`](https://github.com/inspect-js/which-typed-array/commit/9ff452b88fbd8e4419bd768d86d0ea9a87d7e310)

## [v1.1.12](https://github.com/inspect-js/which-typed-array/compare/v1.1.11...v1.1.12) - 2023-10-19

### Commits

- [Fix] somehow node 0.12 - 3 can hit here, and they lack slice but have set [`c28e9b8`](https://github.com/inspect-js/which-typed-array/commit/c28e9b84d6d68ad5f52236ba59c26b06cde6300b)
- [Deps] update `call-bind` [`a648554`](https://github.com/inspect-js/which-typed-array/commit/a64855495106235352ebb3550a860d3bfd4a1ce1)
- [Dev Deps] update `tape` [`7a094d6`](https://github.com/inspect-js/which-typed-array/commit/7a094d6f9219b903c9a9e13c559e68f0e9672b59)

## [v1.1.11](https://github.com/inspect-js/which-typed-array/compare/v1.1.10...v1.1.11) - 2023-07-17

### Commits

- [Fix] `node &lt; v0.6` lacks proper Object toString behavior [`b8fd654`](https://github.com/inspect-js/which-typed-array/commit/b8fd65479c0bd18385378cfae79750ebf7cb6ee7)
- [Dev Deps] update `tape` [`e1734c9`](https://github.com/inspect-js/which-typed-array/commit/e1734c99d79880ab11efa55220498a7a1e887834)

## [v1.1.10](https://github.com/inspect-js/which-typed-array/compare/v1.1.9...v1.1.10) - 2023-07-10

### Commits

- [actions] update rebase action to use reusable workflow [`2c10582`](https://github.com/inspect-js/which-typed-array/commit/2c105820d77274c079cb6d040cb348396e516ef5)
- [Robustness] use `call-bind` [`b2335fd`](https://github.com/inspect-js/which-typed-array/commit/b2335fdfca80840995eea5e6fcfffc6d712279a1)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `tape` [`ad5e41b`](https://github.com/inspect-js/which-typed-array/commit/ad5e41ba18e7d23af1f9b211215c43a64bf75d70)

## [v1.1.9](https://github.com/inspect-js/which-typed-array/compare/v1.1.8...v1.1.9) - 2022-11-02

### Commits

- [Dev Deps] update `aud`, `is-callable`, `tape` [`9a20b3c`](https://github.com/inspect-js/which-typed-array/commit/9a20b3cb8f5d087789a8160395517bffe27b4339)
- [Refactor] use `gopd` instead of `es-abstract` helper [`00157af`](https://github.com/inspect-js/which-typed-array/commit/00157af909842b8b5affa5485d3574ec92d94065)
- [Deps] update `is-typed-array` [`6714240`](https://github.com/inspect-js/which-typed-array/commit/6714240e748cbbb634cb1e405ad762bc52acde66)
- [meta] add `sideEffects` flag [`89b96cc`](https://github.com/inspect-js/which-typed-array/commit/89b96cc3decc78d9621598e94fa1c2bb87eabf2e)

## [v1.1.8](https://github.com/inspect-js/which-typed-array/compare/v1.1.7...v1.1.8) - 2022-05-14

### Commits

- [actions] reuse common workflows [`95ea6c0`](https://github.com/inspect-js/which-typed-array/commit/95ea6c02dc5ec4ed0ee1b9c4692bb060108c8637)
- [meta] use `npmignore` to autogenerate an npmignore file [`d08436a`](https://github.com/inspect-js/which-typed-array/commit/d08436a19cdd76219732f5040a01cdb92ef2820e)
- [readme] add github actions/codecov badges [`35ae3af`](https://github.com/inspect-js/which-typed-array/commit/35ae3af6a0bb328c9d9b9bbb53e47122f269d81a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `tape` [`86e6e3a`](https://github.com/inspect-js/which-typed-array/commit/86e6e3af60b2436f0ff34968d9d6240a23f40528)
- [actions] update codecov uploader [`0aa6e30`](https://github.com/inspect-js/which-typed-array/commit/0aa6e3026ab4198c4364737ed4f0315a2ecc432a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`a881a78`](https://github.com/inspect-js/which-typed-array/commit/a881a785f094e823e1cefe2ae9e4ebe31a8e996e)
- [Refactor] use `for-each` instead of `foreach` [`9dafa03`](https://github.com/inspect-js/which-typed-array/commit/9dafa0377fc5c690059a9d454f1dd4d365c5c902)
- [Deps] update `es-abstract`, `is-typed-array` [`0684022`](https://github.com/inspect-js/which-typed-array/commit/068402297608f321a4ec99ebce741b3eb38fcfdd)
- [Deps] update `es-abstract`, `is-typed-array` [`633a529`](https://github.com/inspect-js/which-typed-array/commit/633a529081b5c48d9675abb8aea425e6e33d528e)

## [v1.1.7](https://github.com/inspect-js/which-typed-array/compare/v1.1.6...v1.1.7) - 2021-08-30

### Commits

- [Refactor] use `globalThis` if available [`2a16d1f`](https://github.com/inspect-js/which-typed-array/commit/2a16d1fd520871ce6b23c60f0bd2113cf33b2533)
- [meta] changelog cleanup [`ba99f56`](https://github.com/inspect-js/which-typed-array/commit/ba99f56b45e6acde7aef4a1f34bb00e44088ccee)
- [Dev Deps] update `@ljharb/eslint-config` [`19a6e04`](https://github.com/inspect-js/which-typed-array/commit/19a6e04ce0094fb3fd6d0d2cbc58d320556ddf50)
- [Deps] update `available-typed-arrays` [`50dbc58`](https://github.com/inspect-js/which-typed-array/commit/50dbc5810a24c468b49409e1f0a79d03501e3dd6)
- [Deps] update `is-typed-array` [`c1b83ea`](https://github.com/inspect-js/which-typed-array/commit/c1b83eae65f042e46b6ae941ac4e814b7965a0f7)

## [v1.1.6](https://github.com/inspect-js/which-typed-array/compare/v1.1.5...v1.1.6) - 2021-08-06

### Fixed

- [Fix] if Symbol.toStringTag exists but is not present, use Object.prototype.toString [`#51`](https://github.com/inspect-js/which-typed-array/issues/51) [`#49`](https://github.com/inspect-js/which-typed-array/issues/49)

### Commits

- [Dev Deps] update `is-callable`, `tape` [`63eb1e3`](https://github.com/inspect-js/which-typed-array/commit/63eb1e3faede3f328bbbb4a5fcffc2e4769cf4ec)
- [Deps] update `is-typed-array` [`c5056f0`](https://github.com/inspect-js/which-typed-array/commit/c5056f0007d4c9434f1fa69eff183109468b4769)

## [v1.1.5](https://github.com/inspect-js/which-typed-array/compare/v1.1.4...v1.1.5) - 2021-08-05

### Commits

- [actions] use `node/install` instead of `node/run`; use `codecov` action [`63fa8dd`](https://github.com/inspect-js/which-typed-array/commit/63fa8dd1dc9c0f0dbbaa16d1de0eb89797324c5d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `is-callable`, `tape` [`1107c74`](https://github.com/inspect-js/which-typed-array/commit/1107c74c52ed6eb4a719faec88e16c4343976d73)
- [Deps] update `available-typed-arrays`, `call-bind`, `es-abstract`, `is-typed-array` [`f953454`](https://github.com/inspect-js/which-typed-array/commit/f953454b2c6f589f09573ddc961431f970c2e1b6)
- [Fix] use `has-tostringtag` to behave correctly in the presence of symbol shams [`8aee720`](https://github.com/inspect-js/which-typed-array/commit/8aee7207abcd72c799ac324b214fbb6ca7ae4a28)
- [meta] use `prepublishOnly` script for npm 7+ [`6c5167b`](https://github.com/inspect-js/which-typed-array/commit/6c5167b4cd06cb62a5487a2e797d8e41cc2970b1)

## [v1.1.4](https://github.com/inspect-js/which-typed-array/compare/v1.1.3...v1.1.4) - 2020-12-05

### Commits

- [meta] npmignore github action workflows [`aa427e7`](https://github.com/inspect-js/which-typed-array/commit/aa427e79a230a985953695a8129ceb6bb7d42527)

## [v1.1.3](https://github.com/inspect-js/which-typed-array/compare/v1.1.2...v1.1.3) - 2020-12-05

### Commits

- [Tests] migrate tests to Github Actions [`803d4dd`](https://github.com/inspect-js/which-typed-array/commit/803d4ddb601ff03e587be792bd452de0e2783d03)
- [Tests] run `nyc` on all tests [`205a13f`](https://github.com/inspect-js/which-typed-array/commit/205a13f7aa172e014ddc2079c84af6ba575581c8)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `is-callable`, `tape` [`97ceb07`](https://github.com/inspect-js/which-typed-array/commit/97ceb070d5aea1c3a696c6f695800ae468bafc0b)
- [actions] add "Allow Edits" workflow [`b140492`](https://github.com/inspect-js/which-typed-array/commit/b14049211eff32bd4149767def4f939483810051)
- [Deps] update `es-abstract`; use `call-bind` where applicable [`2abdb87`](https://github.com/inspect-js/which-typed-array/commit/2abdb871961b4e1b58925115a7d56a9cc5966a02)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`256d34b`](https://github.com/inspect-js/which-typed-array/commit/256d34b8bdb67b8af0e9f83c9a318e54f3340e3b)
- [Dev Deps] update `auto-changelog`; add `aud` [`ddea96f`](https://github.com/inspect-js/which-typed-array/commit/ddea96fe320dbdd0c7d7569812399a7f64d43e04)
- [meta] gitignore nyc output [`8a812bd`](https://github.com/inspect-js/which-typed-array/commit/8a812bd1ce7c5609988fb4fe2e9af2089eccd07d)

## [v1.1.2](https://github.com/inspect-js/which-typed-array/compare/v1.1.1...v1.1.2) - 2020-04-07

### Commits

- [Dev Deps] update `make-arrow-function`, `make-generator-function` [`28c61ef`](https://github.com/inspect-js/which-typed-array/commit/28c61eff4903ff6509f65c2f500858b9cb4636f1)
- [Dev Deps] update `@ljharb/eslint-config` [`a233879`](https://github.com/inspect-js/which-typed-array/commit/a2338798d3a4a3169cda54e322b2f2eb0e976ad0)
- [Dev Deps] update `auto-changelog` [`df0134c`](https://github.com/inspect-js/which-typed-array/commit/df0134c0e20ec6d94993988ad670e1b3cf350bea)
- [Fix] move `foreach` to dependencies [`6ef29c0`](https://github.com/inspect-js/which-typed-array/commit/6ef29c0dbb91a7ec21df7ce8736f99f41efea39e)
- [Tests] only audit prod deps [`eb21044`](https://github.com/inspect-js/which-typed-array/commit/eb210446bd7a433657204d2314ef56fe264c21ad)
- [Deps] update `es-abstract` [`5ef0236`](https://github.com/inspect-js/which-typed-array/commit/5ef02368d9876a1074123aa7725d6759b4f3e358)
- [Dev Deps] update `tape` [`7456037`](https://github.com/inspect-js/which-typed-array/commit/745603728c6c3da8bdddee321e8a9196f4827aa3)
- [Deps] update `available-typed-arrays` [`8a856c9`](https://github.com/inspect-js/which-typed-array/commit/8a856c9aa707c1e6f7a52e834485356b31395ea6)

## [v1.1.1](https://github.com/inspect-js/which-typed-array/compare/v1.1.0...v1.1.1) - 2020-01-24

### Commits

- [Tests] use shared travis-ci configs [`0a627d9`](https://github.com/inspect-js/which-typed-array/commit/0a627d9694d0eabdaee63b19e605584166995a79)
- [meta] add `auto-changelog` [`2a14c58`](https://github.com/inspect-js/which-typed-array/commit/2a14c58b79f72e32ef2078efb40d31a4bf8c197a)
- [meta] remove unused Makefile and associated utilities [`75f7f22`](https://github.com/inspect-js/which-typed-array/commit/75f7f222199f42618c290de363c542b11f5a5632)
- [Tests] up to `node` `v12.10`, `v11.15`, `v10.16`, `v8.16`, `v6.17` [`4162327`](https://github.com/inspect-js/which-typed-array/commit/416232725e7d127cbd886af0f8988dae612a342f)
- [Refactor] use `es-abstract`’s `callBound`, `available-typed-arrays`, `has-symbols` [`9b04a2a`](https://github.com/inspect-js/which-typed-array/commit/9b04a2a14c758600cffcf59485b7b3c85839c266)
- [readme] fix repo URLs, remove testling [`03ed52f`](https://github.com/inspect-js/which-typed-array/commit/03ed52f3ae4fcd35614bcda7e947b14e62009c71)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `replace`, `semver`, `tape` [`bfbcf3e`](https://github.com/inspect-js/which-typed-array/commit/bfbcf3ec9c449bd0089ed805c01a32ba4e7e5938)
- [actions] add automatic rebasing / merge commit blocking [`cc88ac5`](https://github.com/inspect-js/which-typed-array/commit/cc88ac56bcfb71cb26c656ebde4c560a22fadd85)
- [meta] create FUNDING.yml [`acbc723`](https://github.com/inspect-js/which-typed-array/commit/acbc7230929b1256c83df28be4a456eed3e147e9)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `is-callable`, `tape` [`f1ab63e`](https://github.com/inspect-js/which-typed-array/commit/f1ab63e9366027eae2e29398c035181dac164132)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`; add `safe-publish-latest` [`ac9f50b`](https://github.com/inspect-js/which-typed-array/commit/ac9f50b59558933292dff993df2e68eaa44b07e2)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`aaaa15d`](https://github.com/inspect-js/which-typed-array/commit/aaaa15dfb5bd8228c0cfb8f2aba267efb405b0a1)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`602fc9a`](https://github.com/inspect-js/which-typed-array/commit/602fc9a0a7d708236f90c76f592e6a980ecde940)
- [Deps] update `available-typed-arrays`, `is-typed-array` [`b2d69b6`](https://github.com/inspect-js/which-typed-array/commit/b2d69b639bf14344d09f8512dbc060cd4f533161)
- [meta] add `funding` field [`156f613`](https://github.com/inspect-js/which-typed-array/commit/156f613d0ce547c4b15e1ae279198b66e3cef55e)

## [v1.1.0](https://github.com/inspect-js/which-typed-array/compare/v1.0.1...v1.1.0) - 2019-02-16

### Commits

- [Tests] remove `jscs` [`381c9b4`](https://github.com/inspect-js/which-typed-array/commit/381c9b4bd858da1adedf23d8555af3a3ed901a83)
- [Tests] up to `node` `v8.2`, `v7.10`, `v6.11`, `v5.8`; improve matrix; newer npm breaks on older node [`7015c19`](https://github.com/inspect-js/which-typed-array/commit/7015c196ba86540b04d18d9b1d2c368909492023)
- [Tests] up to `node` `v10.0`, `v9.11`, `v8.11`, `v6.14`, `v4.9`; use `nvm install-latest-npm` [`ad67885`](https://github.com/inspect-js/which-typed-array/commit/ad678853e245986720d7650be1c974a9ff3ac814)
- [Tests] up to `node` `v11.6`, `v10.15`, `v8.15`, `v6.16` [`dd94bfb`](https://github.com/inspect-js/which-typed-array/commit/dd94bfb6309a92d1537352f2d1100f9e913ebc01)
- [Refactor] use an array instead of an object for storing Typed Array names [`de98bc1`](https://github.com/inspect-js/which-typed-array/commit/de98bc1d44af92909a34212e276deb5d79ac428a)
- [meta] ignore `test.html` [`06cfb1b`](https://github.com/inspect-js/which-typed-array/commit/06cfb1bc0ca7881d1bd1621fa946a16366cd6afc)
- [Tests] up to `node` `v7.0`, `v6.9`, `v4.6`; improve test matrix [`df76eaa`](https://github.com/inspect-js/which-typed-array/commit/df76eaa39b94b28147e81a89bb587e8aa3e3dba3)
- [New] add `BigInt64Array` and `BigUint64Array` [`d6bca3a`](https://github.com/inspect-js/which-typed-array/commit/d6bca3a68ccfe33f6659a24b770068e89dab1592)
- [Dev Deps] update `jscs`, `nsp`, `eslint` [`f23b45b`](https://github.com/inspect-js/which-typed-array/commit/f23b45b2796bd1f63ddddf28b4b80b9709478cb3)
- [Dev Deps] update `@ljharb/eslint-config`, `eslint`, `semver`, `tape` [`ddb4484`](https://github.com/inspect-js/which-typed-array/commit/ddb4484adc3b45c4396632611556055f3b2f5990)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `is-callable`, `replace`, `semver`, `tape` [`4524e59`](https://github.com/inspect-js/which-typed-array/commit/4524e593e9387c185d5632696c62c1600c0b380f)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `semver` [`1ec7056`](https://github.com/inspect-js/which-typed-array/commit/1ec70568565c479a6168b03e0a5aec6ec9ac5a21)
- [Dev Deps] update `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`799487d`](https://github.com/inspect-js/which-typed-array/commit/799487d666b32d1ae0d27cfededf2f5480c5faea)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `semver` [`8092598`](https://github.com/inspect-js/which-typed-array/commit/8092598998a1f9f8005b4e3d299eb09c96fa2e21)
- [Tests] up to `node` `v11.10` [`a5aabb1`](https://github.com/inspect-js/which-typed-array/commit/a5aabb1910e8408f857a791253487824c7c758d3)
- [Dev Deps] update `@ljharb/eslint-config`, `eslint`, `nsp`, `semver`, `tape` [`277be33`](https://github.com/inspect-js/which-typed-array/commit/277be331d9f05ff95644d6bcd896547ca620cd8e)
- [Tests] use `npm audit` instead of `nsp` [`ee97dc7`](https://github.com/inspect-js/which-typed-array/commit/ee97dc7c5d384d68f60ce6cb5a85d9509e75f72b)
- [Dev Deps] update `tape`, `eslint`, `@ljharb/eslint-config` [`262ffb0`](https://github.com/inspect-js/which-typed-array/commit/262ffb025facb0795b33fbd5131183bdbc0a40f6)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config` [`d6bbcfc`](https://github.com/inspect-js/which-typed-array/commit/d6bbcfc3eea427f0156fbdcf9ae11dbf3745a755)
- [Tests] up to `node` `v6.2` [`2ff89eb`](https://github.com/inspect-js/which-typed-array/commit/2ff89eb91754146c0bc1ae689f37458d84f6e690)
- Only apps should have lockfiles [`e2bc271`](https://github.com/inspect-js/which-typed-array/commit/e2bc271e1e9a6481a2836f892177825a808c331c)
- [Dev Deps] update `nsp`, `eslint`, `@ljharb/eslint-config` [`b79e93b`](https://github.com/inspect-js/which-typed-array/commit/b79e93bf15c871ce0ff24fa3ad61001707eea463)
- [Dev Deps] update `nsp`, `eslint`, `@ljharb/eslint-config` [`016dbff`](https://github.com/inspect-js/which-typed-array/commit/016dbff8c49c32cda7ec80d86006c8a7c43bc40c)
- [Dev Deps] update `eslint`, `tape` [`6ce4bbc`](https://github.com/inspect-js/which-typed-array/commit/6ce4bbc5f6caf632cbcf9ababbfe36e1bf4093d7)
- [Tests] on `node` `v10.1` [`f0683a0`](https://github.com/inspect-js/which-typed-array/commit/f0683a0c17e039e926ecaad4c4c341cd8e5878f1)
- [Tests] up to `node` `v7.2` [`2f29cef`](https://github.com/inspect-js/which-typed-array/commit/2f29cef42d30f87259cd6687c25a79ae4651d0c9)
- [Dev Deps] update `replace` [`73b5ba6`](https://github.com/inspect-js/which-typed-array/commit/73b5ba6e87638d13553985977cab9d1bad33e242)
- [Deps] update `function-bind` [`c8a18c2`](https://github.com/inspect-js/which-typed-array/commit/c8a18c2982e6b126ecc1d4655ec2e53b05535b20)
- [Tests] on `node` `v5.12` [`812102b`](https://github.com/inspect-js/which-typed-array/commit/812102bf223422da8f7a89e5a1308214dd158571)
- [Tests] on `node` `v5.10` [`271584f`](https://github.com/inspect-js/which-typed-array/commit/271584f3a8b10ef68a7d419ac0062b444e63d07c)

## [v1.0.1](https://github.com/inspect-js/which-typed-array/compare/v1.0.0...v1.0.1) - 2016-03-19

### Commits

- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `semver`, `is-callable` [`4a628c5`](https://github.com/inspect-js/which-typed-array/commit/4a628c520d8e080a9fa7e8218947d3b2ceedca72)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `is-callable` [`8e09372`](https://github.com/inspect-js/which-typed-array/commit/8e09372ded877a191cbf777060483227d5071e84)
- [Tests] up to `node` `v5.6`, `v4.3` [`3a35bf9`](https://github.com/inspect-js/which-typed-array/commit/3a35bf9fb9c7f8e6ac1b579ed2754087351ad1a5)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config` [`9410d5e`](https://github.com/inspect-js/which-typed-array/commit/9410d5e35db4b834827b31ea1723bbeebbcde5ba)
- [Fix] `Symbol.toStringTag` is on the super-[[Prototype]] of Float32Array, not the [[Prototype]]. [`7c40a3a`](https://github.com/inspect-js/which-typed-array/commit/7c40a3a05046bbbd188340fb19471ad913e4af05)
- [Tests] up to `node` `v5.9`, `v4.4` [`07878e7`](https://github.com/inspect-js/which-typed-array/commit/07878e7cd23d586ddb9e85a03f675e0a574db246)
- Use the object form of "author" in package.json [`65caa56`](https://github.com/inspect-js/which-typed-array/commit/65caa560d1c0c15c1080b25a9df55c7373c73f08)
- [Tests] use pretest/posttest for linting/security [`c170f7e`](https://github.com/inspect-js/which-typed-array/commit/c170f7ebcf07475d6420f2d2d2d08b1646280cd4)
- [Deps] update `is-typed-array` [`9ab324e`](https://github.com/inspect-js/which-typed-array/commit/9ab324e746a7552b2d9363777fc5c9f5c2e31ce7)
- [Deps] update `function-bind` [`a723142`](https://github.com/inspect-js/which-typed-array/commit/a723142c70a5b6a4f8f5feecc9705619590f4eeb)
- [Deps] update `is-typed-array` [`ed82ce4`](https://github.com/inspect-js/which-typed-array/commit/ed82ce4e8ecc657fc6e839d23ef6347497bc93be)
- [Tests] on `node` `v4.2` [`f581c20`](https://github.com/inspect-js/which-typed-array/commit/f581c2031990668894a8e5a08eaf01a2548e822c)

## v1.0.0 - 2015-10-05

### Commits

- Dotfiles / Makefile [`667f89a`](https://github.com/inspect-js/which-typed-array/commit/667f89a9046502594e2559dbf5568e062af3b770)
- Tests. [`a14d05e`](https://github.com/inspect-js/which-typed-array/commit/a14d05ef443d2ac678cb0567befc0abf8cf21709)
- package.json [`560b1aa`](https://github.com/inspect-js/which-typed-array/commit/560b1aa4f8bbc5d41d9cee96c93faf08c25be0e5)
- Read me [`a22096e`](https://github.com/inspect-js/which-typed-array/commit/a22096e05773f93b34e672d3f743ec6f1963bc24)
- Implementation [`0b1ae28`](https://github.com/inspect-js/which-typed-array/commit/0b1ae2848372f6256cf075d687e3722878e67aca)
- Initial commit [`4b32f0a`](https://github.com/inspect-js/which-typed-array/commit/4b32f0a9d32165d6ab91797d6971ea83cf4ce9da)
